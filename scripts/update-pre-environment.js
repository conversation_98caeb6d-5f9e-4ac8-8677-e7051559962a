const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updatePreEnvironment() {
  try {
    console.log('正在更新 pre 环境名称为 PRE...')
    
    // 查找现有的 pre 环境
    const preEnv = await prisma.environment.findUnique({
      where: { name: 'pre' }
    })
    
    if (preEnv) {
      // 更新环境名称为大写
      await prisma.environment.update({
        where: { name: 'pre' },
        data: { name: 'PRE' }
      })
      console.log('已将 pre 环境更新为 PRE')
    } else {
      console.log('未找到 pre 环境，可能已经是 PRE 或不存在')
    }
    
    // 显示所有环境
    const allEnvironments = await prisma.environment.findMany({
      orderBy: { name: 'asc' }
    })
    
    console.log('\n当前所有环境:')
    allEnvironments.forEach(env => {
      console.log(`- ${env.name}: ${env.displayName}`)
    })
    
  } catch (error) {
    console.error('更新环境失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updatePreEnvironment()
