-- 清理全局版本数据的SQL脚本
-- 在应用schema更改之前运行此脚本

-- 1. 检查是否存在全局版本（namespaceId为NULL的release）
SELECT 
    id, 
    version, 
    description, 
    status,
    createdAt
FROM releases 
WHERE namespaceId IS NULL;

-- 2. 如果存在全局版本，需要先处理关联的翻译键
-- 将关联到全局版本的翻译键的releaseId设为NULL
UPDATE translation_keys 
SET releaseId = NULL 
WHERE releaseId IN (
    SELECT id FROM releases WHERE namespaceId IS NULL
);

-- 3. 删除所有全局版本
DELETE FROM releases WHERE namespaceId IS NULL;

-- 4. 验证清理结果
SELECT COUNT(*) as global_releases_count FROM releases WHERE namespaceId IS NULL;
-- 应该返回0

-- 5. 检查是否还有孤立的翻译键（没有namespace的）
SELECT COUNT(*) as orphaned_translation_keys 
FROM translation_keys tk
LEFT JOIN namespaces n ON tk.namespaceId = n.id
WHERE n.id IS NULL;
-- 应该返回0

-- 注意：运行此脚本后，需要更新schema使namespaceId变为NOT NULL
-- ALTER TABLE releases MODIFY COLUMN namespaceId VARCHAR(191) NOT NULL;
