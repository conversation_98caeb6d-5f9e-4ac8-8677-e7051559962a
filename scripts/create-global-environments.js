const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createGlobalEnvironments() {
  try {
    console.log('正在创建全局环境...')
    
    // 定义默认环境
    const defaultEnvironments = [
      {
        name: 'dev',
        displayName: '开发环境',
        description: '用于开发和调试的环境'
      },
      {
        name: 'PRE',
        displayName: '预发布环境',
        description: '用于测试和验证的环境'
      },
      {
        name: 'prod',
        displayName: '生产环境',
        description: '线上正式环境'
      }
    ]
    
    for (const env of defaultEnvironments) {
      // 检查环境是否已存在
      const existing = await prisma.environment.findUnique({
        where: { name: env.name }
      })
      
      if (!existing) {
        const created = await prisma.environment.create({
          data: env
        })
        console.log(`创建环境: ${created.name} (${created.displayName})`)
      } else {
        // 更新显示名称和描述
        const updated = await prisma.environment.update({
          where: { name: env.name },
          data: {
            displayName: env.displayName,
            description: env.description
          }
        })
        console.log(`更新环境: ${updated.name} (${updated.displayName})`)
      }
    }
    
    console.log('全局环境创建/更新完成！')
    
    // 显示所有环境
    const allEnvironments = await prisma.environment.findMany({
      orderBy: { createdAt: 'asc' }
    })
    
    console.log('\n当前所有环境:')
    allEnvironments.forEach(env => {
      console.log(`- ${env.name}: ${env.displayName}`)
    })
    
  } catch (error) {
    console.error('创建全局环境失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createGlobalEnvironments()
