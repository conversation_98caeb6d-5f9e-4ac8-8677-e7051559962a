const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateEnvironments() {
  try {
    console.log('正在更新环境的 displayName...')
    
    // 获取所有环境
    const environments = await prisma.environment.findMany()
    
    console.log(`找到 ${environments.length} 个环境`)
    
    for (const env of environments) {
      let displayName = env.displayName
      
      // 如果 displayName 为空，根据 name 生成一个
      if (!displayName || displayName === '') {
        switch (env.name.toLowerCase()) {
          case 'dev':
          case 'development':
            displayName = '开发环境'
            break
          case 'test':
          case 'testing':
            displayName = '测试环境'
            break
          case 'staging':
            displayName = '预发布环境'
            break
          case 'prod':
          case 'production':
            displayName = '生产环境'
            break
          default:
            displayName = env.name + '环境'
        }
        
        // 更新环境
        await prisma.environment.update({
          where: { id: env.id },
          data: { displayName }
        })
        
        console.log(`更新环境 ${env.name} -> ${displayName}`)
      } else {
        console.log(`环境 ${env.name} 已有 displayName: ${displayName}`)
      }
    }
    
    console.log('环境更新完成！')
  } catch (error) {
    console.error('更新环境失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateEnvironments()
