-- 更新TranslationKey表的唯一约束
-- 从 (key, namespaceId) 改为 (key, namespaceId, releaseId)
-- 支持不同版本中有相同的翻译键

-- 1. 删除旧的唯一约束
ALTER TABLE `translation_keys` DROP INDEX `translation_keys_key_namespaceId_key`;

-- 2. 添加新的唯一约束 (key, namespaceId, releaseId)
ALTER TABLE `translation_keys` ADD UNIQUE INDEX `translation_keys_key_namespaceId_releaseId_key` (`key`, `namespaceId`, `releaseId`);

-- 验证约束是否创建成功
SHOW INDEX FROM `translation_keys` WHERE Key_name = 'translation_keys_key_namespaceId_releaseId_key';

-- 可选：查看表结构确认
-- DESCRIBE `translation_keys`;
