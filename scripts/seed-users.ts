import { PrismaClient } from "@/generated/prisma"

const prisma = new PrismaClient()

async function seedUsers() {
  try {
    // 创建测试用户
    const testUser = await prisma.user.upsert({
      where: { username: "admin" },
      update: {},
      create: {
        username: "admin",
        email: "<EMAIL>",
        hashedPassword: "hashed_password_placeholder"
      }
    })

    console.log("测试用户创建成功:", testUser)

    // 创建一个测试模块
    const testModule = await prisma.module.upsert({
      where: { name: "common" },
      update: {},
      create: {
        name: "common",
        description: "通用翻译模块"
      }
    })

    console.log("测试模块创建成功:", testModule)

    // 创建一个初始版本
    const initialRelease = await prisma.release.upsert({
      where: { version: "v1.0.0" },
      update: {},
      create: {
        version: "v1.0.0",
        description: "初始版本",
        status: "PUBLISHED",
        isCurrent: true,
        publishedAt: new Date(),
        createdBy: testUser.id
      }
    })

    console.log("初始版本创建成功:", initialRelease)

    // 创建一些测试翻译键
    const translationKeys = [
      {
        key: "welcome",
        platform: "WEB" as const,
        moduleId: testModule.id,
        description: "欢迎消息",
        createdBy: testUser.id,
        releaseId: initialRelease.id,
        zh_CN: "欢迎",
        en_US: "Welcome",
        ja_JP: "ようこそ"
      },
      {
        key: "goodbye",
        platform: "WEB" as const,
        moduleId: testModule.id,
        description: "告别消息",
        createdBy: testUser.id,
        releaseId: initialRelease.id,
        zh_CN: "再见",
        en_US: "Goodbye",
        ja_JP: "さようなら"
      }
    ]

    for (const keyData of translationKeys) {
      const translationKey = await prisma.translationKey.upsert({
        where: {
          key_platform_moduleId_releaseId: {
            key: keyData.key,
            platform: keyData.platform,
            moduleId: keyData.moduleId,
            releaseId: keyData.releaseId
          }
        },
        update: {},
        create: keyData
      })
      console.log("翻译键创建成功:", translationKey.key)
    }

    console.log("数据种子创建完成！")

  } catch (error) {
    console.error("创建数据种子失败:", error)
  } finally {
    await prisma.$disconnect()
  }
}

seedUsers()
