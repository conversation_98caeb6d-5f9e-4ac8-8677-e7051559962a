const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function cleanupEnvironments() {
  try {
    console.log('正在清理环境...')
    
    // 删除 staging 环境（保留 pre）
    const stagingEnv = await prisma.environment.findUnique({
      where: { name: 'staging' }
    })
    
    if (stagingEnv) {
      await prisma.environment.delete({
        where: { name: 'staging' }
      })
      console.log('删除了 staging 环境')
    }
    
    // 显示剩余环境
    const allEnvironments = await prisma.environment.findMany({
      orderBy: { name: 'asc' }
    })
    
    console.log('\n当前所有环境:')
    allEnvironments.forEach(env => {
      console.log(`- ${env.name}: ${env.displayName}`)
    })
    
  } catch (error) {
    console.error('清理环境失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanupEnvironments()
