// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id              String   @id @default(cuid())
  email           String   @unique
  username        String   @unique
  hashedPassword  String
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  createdTranslationKeys TranslationKey[] @relation("TranslationKeyCreator")
  createdReleases        Release[]        @relation("ReleaseCreator")
  createdNamespaces      Namespace[]      @relation("NamespaceCreator")

  @@map("users")
}



// 命名空间：功能模块分组，简化后不再依赖应用
model Namespace {
  id          String   @id @default(cuid())
  name        String   @unique        // auth, user, order - 全局唯一
  displayName String                  // 认证模块, 用户模块, 订单模块
  description String?
  createdBy   String   @default("cme29c55n0000zuy9yqi7iqnn") // 创建者ID，默认为测试用户
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  creator User             @relation("NamespaceCreator", fields: [createdBy], references: [id])
  keys    TranslationKey[]
  releases Release[]       // 命名空间可以有多个版本

  @@map("namespaces")
}

model TranslationKey {
  id          String   @id @default(cuid())
  key         String
  namespaceId String
  description String?
  createdBy   String
  releaseId   String?  // 版本关联改为可选，支持未发布的翻译键
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 19种语言的翻译内容
  zh_CN       String?  // 简体中文
  zh_TW       String?  // 繁体中文
  en_US       String?  // 英语(美国)
  ja_JP       String?  // 日语(日本)
  ko_KR       String?  // 韩语(韩国)
  fr_FR       String?  // 法语(法国)
  de_DE       String?  // 德语(德国)
  es_ES       String?  // 西班牙语(西班牙)
  pt_PT       String?  // 葡萄牙语(葡萄牙)
  ru_RU       String?  // 俄语(俄罗斯)
  ar_SA       String?  // 阿拉伯语(沙特阿拉伯)
  bn_BD       String?  // 孟加拉语(孟加拉国)
  hi_IN       String?  // 印地语(印度)
  th_TH       String?  // 泰语(泰国)
  vi_VN       String?  // 越南语(越南)
  id_ID       String?  // 印尼语(印尼)
  tr_TR       String?  // 土耳其语(土耳其)
  ur_PK       String?  // 乌尔都语(巴基斯坦)
  fa_IR       String?  // 波斯语(伊朗)

  // Relations with foreign keys
  namespace   Namespace @relation(fields: [namespaceId], references: [id], onDelete: Cascade)
  creator     User      @relation("TranslationKeyCreator", fields: [createdBy], references: [id])
  release     Release?  @relation(fields: [releaseId], references: [id], onDelete: SetNull)

  @@unique([key, namespaceId, releaseId]) // 同一版本内键唯一，支持跨版本重复
  @@map("translation_keys")
}

enum ReleaseStatus {
  DRAFT      // 草稿状态，可编辑
  PUBLISHED  // 已发布，不可编辑
  ARCHIVED   // 已归档
}

model Release {
  id            String        @id @default(cuid())
  version       String        @unique // v1.0.0，版本号全局唯一
  description   String?
  status        ReleaseStatus @default(DRAFT)
  namespaceId   String        // 版本必须属于特定命名空间，不支持全局版本
  changes       String        @default("[]") // JSON string of changes
  publishedAt   DateTime?     // 发布时间
  basedOnId     String?       // 基于哪个版本创建
  createdBy     String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  namespace       Namespace        @relation(fields: [namespaceId], references: [id], onDelete: Cascade)
  creator         User             @relation("ReleaseCreator", fields: [createdBy], references: [id])
  translationKeys TranslationKey[]
  
  // 版本继承关系
  basedOn             Release?         @relation("ReleaseInheritance", fields: [basedOnId], references: [id])
  derivedVersions     Release[]        @relation("ReleaseInheritance")

  @@map("releases")
}


