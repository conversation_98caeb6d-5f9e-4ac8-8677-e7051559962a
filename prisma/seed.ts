import { PrismaClient } from '../src/generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始初始化数据...')

  // 创建测试用户
  const hashedPassword = await bcrypt.hash('123456', 10)
  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'admin',
      hashedPassword,
      isActive: true
    }
  })
  console.log('创建用户:', user.username)

  // 创建示例应用：用户服务
  const userServiceApp = await prisma.application.create({
    data: {
      name: 'user-service',
      displayName: '用户服务',
      description: '负责用户认证、注册、个人资料管理等功能'
    }
  })
  console.log('创建应用:', userServiceApp.displayName)

  // 为用户服务创建环境
  const devEnv = await prisma.environment.create({
    data: {
      name: 'DEV',
      applicationId: userServiceApp.id,
      description: '开发环境'
    }
  })

  const testEnv = await prisma.environment.create({
    data: {
      name: 'TEST',
      applicationId: userServiceApp.id,
      description: '测试环境'
    }
  })

  const prodEnv = await prisma.environment.create({
    data: {
      name: 'PROD',
      applicationId: userServiceApp.id,
      description: '生产环境'
    }
  })
  console.log('创建环境: DEV, TEST, PROD')

  // 为用户服务创建命名空间
  const commonNamespace = await prisma.namespace.create({
    data: {
      name: 'common',
      displayName: '通用',
      applicationId: userServiceApp.id,
      description: '通用翻译内容'
    }
  })

  const validationNamespace = await prisma.namespace.create({
    data: {
      name: 'validation',
      displayName: '验证',
      applicationId: userServiceApp.id,
      description: '验证相关翻译'
    }
  })

  const businessNamespace = await prisma.namespace.create({
    data: {
      name: 'business',
      displayName: '业务',
      applicationId: userServiceApp.id,
      description: '业务相关翻译'
    }
  })
  console.log('创建命名空间: 通用, 验证, 业务')

  // 在通用命名空间下创建模块
  const authModule = await prisma.module.create({
    data: {
      name: 'auth',
      displayName: '认证模块',
      description: '用户认证相关功能',
      applicationId: userServiceApp.id,
      namespaceId: commonNamespace.id
    }
  })

  const userModule = await prisma.module.create({
    data: {
      name: 'user',
      displayName: '用户模块',
      description: '用户管理相关功能',
      applicationId: userServiceApp.id,
      namespaceId: businessNamespace.id
    }
  })
  console.log('创建模块: 认证模块, 用户模块')

  // 为开发环境创建初始版本
  const devRelease = await prisma.release.create({
    data: {
      version: 'v1.0.0-dev',
      description: '开发版本',
      status: 'DRAFT',
      environmentId: devEnv.id,
      createdBy: user.id
    }
  })
  console.log('创建开发版本:', devRelease.version)

  // 创建一些示例翻译键
  await prisma.translationKey.create({
    data: {
      key: 'auth.login.success',
      platform: 'SERVER',
      moduleId: authModule.id,
      description: '登录成功提示',
      createdBy: user.id,
      releaseId: devRelease.id,
      zh_CN: '登录成功',
      en_US: 'Login successful',
      ja_JP: 'ログイン成功',
      ko_KR: '로그인 성공'
    }
  })

  await prisma.translationKey.create({
    data: {
      key: 'auth.login.failed',
      platform: 'SERVER',
      moduleId: authModule.id,
      description: '登录失败提示',
      createdBy: user.id,
      releaseId: devRelease.id,
      zh_CN: '用户名或密码错误',
      en_US: 'Invalid username or password',
      ja_JP: 'ユーザー名またはパスワードが間違っています',
      ko_KR: '사용자명 또는 비밀번호가 틀렸습니다'
    }
  })

  await prisma.translationKey.create({
    data: {
      key: 'user.not.found',
      platform: 'SERVER',
      moduleId: userModule.id,
      description: '用户不存在',
      createdBy: user.id,
      releaseId: devRelease.id,
      zh_CN: '用户不存在',
      en_US: 'User not found',
      ja_JP: 'ユーザーが見つかりません',
      ko_KR: '사용자를 찾을 수 없습니다'
    }
  })

  console.log('创建示例翻译键')

  // 创建第二个应用：订单服务
  const orderServiceApp = await prisma.application.create({
    data: {
      name: 'order-service',
      displayName: '订单服务',
      description: '负责订单管理、支付处理等功能'
    }
  })

  // 为订单服务创建环境
  await prisma.environment.createMany({
    data: [
      {
        name: 'DEV',
        applicationId: orderServiceApp.id,
        description: '开发环境'
      },
      {
        name: 'TEST',
        applicationId: orderServiceApp.id,
        description: '测试环境'
      },
      {
        name: 'PROD',
        applicationId: orderServiceApp.id,
        description: '生产环境'
      }
    ]
  })

  // 为订单服务创建命名空间
  await prisma.namespace.createMany({
    data: [
      {
        name: 'common',
        displayName: '通用',
        applicationId: orderServiceApp.id,
        description: '通用翻译内容'
      },
      {
        name: 'validation',
        displayName: '验证',
        applicationId: orderServiceApp.id,
        description: '验证相关翻译'
      },
      {
        name: 'business',
        displayName: '业务',
        applicationId: orderServiceApp.id,
        description: '业务相关翻译'
      },
      {
        name: 'payment',
        displayName: '支付',
        applicationId: orderServiceApp.id,
        description: '支付相关翻译'
      }
    ]
  })

  console.log('创建订单服务应用和环境')
  console.log('数据初始化完成！')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
