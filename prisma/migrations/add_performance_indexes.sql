-- 性能优化索引
-- 翻译键核心查询索引
CREATE INDEX idx_translation_keys_namespace_id ON translation_keys(namespaceId);
CREATE INDEX idx_translation_keys_created_at ON translation_keys(createdAt);
CREATE INDEX idx_translation_keys_key ON translation_keys(`key`);
CREATE INDEX idx_translation_keys_composite ON translation_keys(namespaceId, createdAt);

-- 命名空间查询索引
CREATE INDEX idx_namespaces_application_id ON namespaces(applicationId);
CREATE INDEX idx_namespaces_created_at ON namespaces(createdAt);
CREATE INDEX idx_namespaces_name ON namespaces(`name`);

-- 环境查询索引
CREATE INDEX idx_environments_application_id ON environments(applicationId);
CREATE INDEX idx_environments_created_at ON environments(createdAt);

-- 版本查询索引
CREATE INDEX idx_releases_environment_id ON releases(environmentId);
CREATE INDEX idx_releases_created_at ON releases(createdAt);
CREATE INDEX idx_releases_status ON releases(`status`);
CREATE INDEX idx_releases_composite ON releases(environmentId, createdAt);

-- 应用表索引
CREATE INDEX idx_applications_created_at ON applications(createdAt);
CREATE INDEX idx_applications_name ON applications(`name`);

-- 用户表索引（如果存在）
-- CREATE INDEX idx_users_username ON users(username);
-- CREATE INDEX idx_users_email ON users(email);
