# i18n 管理系统

高性能国际化翻译服务管理平台，基于 Next.js、TypeScript、Prisma 和 MySQL 构建。

## 功能特性

- 🌍 **多语言管理** - 支持多种语言的翻译内容管理
- 📦 **模块化组织** - 通过模块和平台对翻译内容进行分类管理
- 🚀 **版本发布** - 支持全量发布和灰度发布功能
- 🔐 **用户认证** - 基于 NextAuth.js 的安全认证系统
- 💾 **数据缓存** - 集成 Redis 缓存提升性能
- 🎨 **现代 UI** - 基于 Shadcn/ui 的美观界面

## 技术栈

- **前端**: Next.js 15, TypeScript, Tailwind CSS, Shadcn/ui
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: MySQL
- **缓存**: Redis
- **认证**: NextAuth.js
- **翻译服务**: Google Cloud Translate, AWS Translate

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量文件并配置：

```bash
cp .env.example .env
```

在 `.env` 文件中配置以下变量：

```env
# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/i18n"

# NextAuth 配置
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"

# Redis 配置
REDIS_URL="redis://localhost:6379"

# Google Cloud Translate 配置
GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account.json"
GOOGLE_PROJECT_ID="your-google-project-id"

# AWS Translate 配置
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
```

### 3. 数据库设置

启动 PostgreSQL 数据库，然后运行迁移：

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev --name init
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   │   ├── auth/         # 认证相关 API
│   │   ├── modules/      # 模块管理 API
│   │   ├── translations/ # 翻译管理 API
│   │   └── releases/     # 发布管理 API
│   ├── login/            # 登录页面
│   └── page.tsx          # 主页
├── components/            # React 组件
│   ├── ui/               # UI 组件库
│   ├── Dashboard.tsx     # 主仪表板
│   ├── TranslationManagement.tsx
│   ├── ModuleManagement.tsx
│   └── ReleaseManagement.tsx
├── lib/                  # 工具库
│   └── db.ts            # 数据库客户端
└── generated/           # Prisma 生成的文件
```

## 数据库模型

### 用户 (User)
- 用户认证和权限管理

### 模块 (Module)
- 翻译内容的组织单位

### 翻译键 (TranslationKey)
- 具体的翻译项，关联到模块和平台

### 翻译 (Translation)
- 多语言翻译内容，支持版本管理

### 发布 (Release)
- 版本发布记录，支持灰度发布

## 主要功能

### 翻译管理
- 多语言翻译内容的增删改查
- 按模块、平台、语言筛选
- 支持批量导入导出

### 模块管理
- 创建和管理翻译模块
- 为模块添加翻译键
- 支持平台分类（SERVER、CLIENT、WEB）

### 发布管理
- 创建版本发布
- 支持全量发布和灰度发布
- 发布历史追踪

### 用户认证
- 用户注册和登录
- 基于 JWT 的会话管理
- 权限控制

## 开发说明

### 添加新的 UI 组件

```bash
npx shadcn@latest add [component-name]
```

### 数据库迁移

```bash
# 修改 schema.prisma 后运行
npx prisma migrate dev --name [migration-name]
```

### 生成 Prisma 客户端

```bash
npx prisma generate
```

## 部署

### 生产构建

```bash
npm run build
npm start
```

### Docker 部署

```bash
# 构建镜像
docker build -t i18n-app .

# 运行容器
docker run -p 3000:3000 i18n-app
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 支持

如有问题或建议，请创建 [Issue](https://github.com/your-repo/i18n/issues)。
