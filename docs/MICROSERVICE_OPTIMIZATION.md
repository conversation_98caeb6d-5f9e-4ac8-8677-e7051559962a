# 微服务i18n后台管理系统优化方案

## 当前架构分析

### 优点：
1. ✅ **版本控制完善** - 支持草稿、发布、归档状态
2. ✅ **19种语言支持** - 覆盖主要国际化需求
3. ✅ **模块化组织** - 按功能模块组织翻译键
4. ✅ **平台区分** - 支持服务端、客户端、Web区分

### 微服务场景优化建议：

#### 1. 添加应用(Application)概念
```sql
-- 新增应用表
CREATE TABLE applications (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,  -- user-service, order-service
  description TEXT,
  environments JSON,  -- ["dev", "test", "prod"]
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 修改Module表，关联到应用
ALTER TABLE modules ADD COLUMN application_id VARCHAR(255);
ALTER TABLE modules ADD FOREIGN KEY (application_id) REFERENCES applications(id);
```

#### 2. 添加环境(Environment)概念
```sql
-- 新增环境表
CREATE TABLE environments (
  id VARCHAR(255) PRIMARY KEY,
  name ENUM('dev', 'test', 'prod') NOT NULL,
  application_id VARCHAR(255) NOT NULL,
  current_release_id VARCHAR(255),  -- 当前生效版本
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (application_id) REFERENCES applications(id),
  FOREIGN KEY (current_release_id) REFERENCES releases(id),
  UNIQUE KEY unique_app_env (application_id, name)
);

-- 修改Release表，关联到环境
ALTER TABLE releases ADD COLUMN environment_id VARCHAR(255);
ALTER TABLE releases ADD FOREIGN KEY (environment_id) REFERENCES environments(id);
```

#### 3. 添加命名空间(Namespace)概念
```sql
-- 新增命名空间表
CREATE TABLE namespaces (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,  -- common, validation, business
  application_id VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (application_id) REFERENCES applications(id),
  UNIQUE KEY unique_app_namespace (application_id, name)
);

-- 修改Module表，关联到命名空间
ALTER TABLE modules ADD COLUMN namespace_id VARCHAR(255);
ALTER TABLE modules ADD FOREIGN KEY (namespace_id) REFERENCES namespaces(id);
```

## 界面架构重构

### 1. 导航结构优化
```
当前: 翻译管理 | 模块管理 | 版本管理

优化后: 应用管理 | 翻译配置 | 版本发布 | 环境管理
```

### 2. 页面层级结构
```
应用列表页
├── 应用详情页
│   ├── 环境管理 (dev/test/prod)
│   ├── 命名空间管理 (common/validation/business)
│   └── 模块管理
└── 翻译配置页
    ├── 按应用+环境+命名空间筛选
    ├── 翻译键CRUD操作
    └── 批量导入/导出功能
```

### 3. 工作流程优化
```
微服务开发流程:
1. 创建应用 (user-service)
2. 配置环境 (dev/test/prod)
3. 创建命名空间 (common, validation, business)
4. 在dev环境添加翻译键
5. 发布到test环境测试
6. 发布到prod环境上线
```

## 实际使用场景示例

### 场景1: 用户服务国际化
```
应用: user-service
环境: dev, test, prod
命名空间:
  - common: 通用消息 (success, loading等)
  - validation: 验证消息 (email.invalid, password.weak等)
  - business: 业务消息 (user.not.found, user.created等)

翻译键示例:
  - user.not.found: "用户不存在" / "User not found"
  - email.invalid: "邮箱格式不正确" / "Invalid email format"
  - password.weak: "密码强度不够" / "Password too weak"
```

### 场景2: 订单服务国际化
```
应用: order-service
环境: dev, test, prod
命名空间:
  - common: 通用消息
  - validation: 订单验证消息
  - business: 订单业务消息
  - payment: 支付相关消息

翻译键示例:
  - order.not.found: "订单不存在" / "Order not found"
  - payment.failed: "支付失败" / "Payment failed"
  - order.cancelled: "订单已取消" / "Order cancelled"
```

## 界面设计建议

### 1. 应用管理页面
- 应用列表卡片式展示
- 每个应用显示环境状态、翻译键数量
- 快速切换环境的操作

### 2. 翻译配置页面
- 左侧：应用+环境+命名空间树形结构
- 右侧：翻译键列表和编辑区域
- 支持实时搜索和筛选

### 3. 版本发布页面
- 环境间版本对比
- 一键发布到下一环境
- 发布历史和回滚功能
