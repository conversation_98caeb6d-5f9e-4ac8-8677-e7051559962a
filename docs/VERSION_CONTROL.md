# i18n 版本控制系统设计

## 核心概念

### 版本状态
- **DRAFT（草稿）**: 开发中的版本，可以自由修改翻译键
- **PUBLISHED（已发布）**: 生产环境使用的版本，**不可修改**
- **ARCHIVED（已归档）**: 历史版本，不再使用但保留记录

### 版本控制规则

#### 1. 草稿版本（DRAFT）
- ✅ 可以添加新的翻译键
- ✅ 可以修改现有翻译键的内容
- ✅ 可以删除翻译键
- ✅ 可以发布为正式版本

#### 2. 已发布版本（PUBLISHED）
- ❌ **不能修改**任何翻译键内容
- ❌ **不能删除**翻译键
- ❌ **不能添加**新的翻译键
- ✅ 可以设置为当前使用版本
- ✅ 可以归档

#### 3. 已归档版本（ARCHIVED）
- ❌ 完全不可修改
- ✅ 可以查看历史记录
- ✅ 可以基于此版本创建新版本

## 工作流程

### 开发流程
1. **创建新版本**: 创建一个新的 DRAFT 版本
2. **开发翻译**: 在草稿版本中添加/修改翻译键
3. **测试验证**: 在测试环境验证翻译内容
4. **发布版本**: 将草稿版本发布为 PUBLISHED 状态
5. **部署使用**: 设置为当前版本（isCurrent = true）

### 修改已发布内容
如果需要修改已发布的翻译内容：

1. **基于当前版本创建新版本**:
   ```
   POST /api/releases/{sourceReleaseId}/clone
   {
     "version": "v1.1.0",
     "description": "修复翻译问题",
     "createdBy": "userId"
   }
   ```
   
   系统会自动：
   - 创建新的 DRAFT 版本
   - 复制源版本的所有翻译键
   - 设置 basedOnId 指向源版本
   - 记录克隆操作到 changes 字段

2. **在新版本中进行修改**:
   - 修改需要更新的翻译键
   - 添加新的翻译键
   - 删除不需要的翻译键

3. **发布新版本**:
   ```
   状态: DRAFT → PUBLISHED
   publishedAt: 设置发布时间
   ```

4. **切换当前版本**:
   ```
   旧版本.isCurrent = false
   新版本.isCurrent = true
   ```

### 版本回滚
回滚到历史版本：

1. **选择目标版本**: 选择要回滚到的已发布版本
2. **切换当前版本**:
   ```
   当前版本.isCurrent = false
   目标版本.isCurrent = true
   ```
3. **应用生效**: 系统立即使用目标版本的翻译内容

## 版本克隆功能

### API 接口
```typescript
POST /api/releases/{sourceReleaseId}/clone
{
  "version": "v1.1.0",
  "description": "修复翻译问题",
  "createdBy": "userId"
}
```

### 克隆过程
1. **验证源版本**: 检查源版本是否存在
2. **检查版本号**: 确保新版本号唯一
3. **创建新版本**: 状态为 DRAFT，设置 basedOnId
4. **复制翻译键**: 复制所有翻译内容到新版本
5. **记录变更**: 在 changes 字段记录克隆操作

### 前端组件
```tsx
import { VersionClone } from "@/components/VersionClone"

<VersionClone
  sourceReleaseId="release-id"
  sourceVersion="v1.0.0"
  onCloneSuccess={(newRelease) => {
    console.log("新版本创建成功:", newRelease)
  }}
/>
```

## 数据模型关系

```
Release (版本)
├── status: DRAFT | PUBLISHED | ARCHIVED
├── isCurrent: 是否为当前使用版本
├── publishedAt: 发布时间
├── basedOnId: 基于哪个版本创建
├── basedOn: 父版本关系
├── derivedVersions: 子版本列表
└── translationKeys: 该版本包含的所有翻译键

TranslationKey (翻译键)
├── releaseId: 必须关联到某个版本
├── key: 翻译键名
├── platform: 平台类型
├── moduleId: 所属模块
└── [19种语言字段]: 翻译内容
```

## API 设计要点

### 权限控制
- 只有 DRAFT 状态的版本允许修改翻译键
- PUBLISHED 和 ARCHIVED 状态的版本拒绝修改操作

### 版本切换
- 同时只能有一个版本设置为 isCurrent = true
- 切换版本时需要原子性操作

### 数据一致性
- 删除版本时，关联的翻译键也会被删除（CASCADE）
- 发布版本时，验证所有必要的翻译内容是否完整

这种设计确保了：
1. **数据完整性**: 已发布版本不可变
2. **版本追溯**: 可以回滚到任何历史版本
3. **开发灵活性**: 草稿版本可以自由修改
4. **生产稳定性**: 生产环境使用的版本是稳定的
