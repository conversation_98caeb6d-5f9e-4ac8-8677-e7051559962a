{"name": "i18n", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 8113", "build": "next build", "start": "next start -p 8113", "lint": "next lint"}, "dependencies": {"@google-cloud/translate": "^9.1.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.0.11", "ldapts": "^8.0.9", "lucide-react": "^0.522.0", "mysql2": "^3.11.4", "next": "15.3.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "redis": "^5.5.6", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}