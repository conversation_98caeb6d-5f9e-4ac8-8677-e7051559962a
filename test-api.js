// 测试版本创建 API
const testCreateRelease = async () => {
  try {
    const response = await fetch("http://localhost:3000/api/releases", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        version: "v1.1.0",
        description: "测试版本",
        createdBy: "cme1m63j700008oqo1spfkhe6"
      })
    })

    const data = await response.json()
    console.log("Response status:", response.status)
    console.log("Response data:", data)

    if (response.ok) {
      console.log("✅ 版本创建成功")
    } else {
      console.log("❌ 版本创建失败:", data.error)
    }
  } catch (error) {
    console.error("❌ 请求错误:", error)
  }
}

testCreateRelease()
