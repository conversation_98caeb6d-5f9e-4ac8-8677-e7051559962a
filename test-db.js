const { PrismaClient } = require('./src/generated/prisma');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Testing database connection...');
    
    // 测试连接
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // 查询现有用户
    const users = await prisma.user.findMany();
    console.log('📊 Existing users:', users.length);
    
    // 尝试创建用户
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    const user = await prisma.user.create({
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        hashedPassword: hashedPassword,
      }
    });
    
    console.log('✅ User created successfully:', user);
    
    // 清理测试用户
    await prisma.user.delete({
      where: { id: user.id }
    });
    
    console.log('✅ Test user cleaned up');
    
  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection(); 