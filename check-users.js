const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true
      }
    })
    
    console.log('Available users:')
    users.forEach(user => {
      console.log(`ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`)
    })
    
    if (users.length === 0) {
      console.log('No users found. Creating a test user...')
      const testUser = await prisma.user.create({
        data: {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'hashedpassword'
        }
      })
      console.log(`Created test user: ID: ${testUser.id}, Username: ${testUser.username}`)
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
