import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface CurrentUser {
  id: string
  username: string
  email: string
}

export function useCurrentUser() {
  const { data: session } = useSession()
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCurrentUser = async () => {
      if (!session?.user?.name) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const response = await fetch('/api/auth/me')
        
        if (response.ok) {
          const userData = await response.json()
          setCurrentUser(userData)
          setError(null)
        } else {
          setError('获取用户信息失败')
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        setError('获取用户信息失败')
      } finally {
        setLoading(false)
      }
    }

    fetchCurrentUser()
  }, [session])

  return {
    currentUser,
    currentUserId: currentUser?.id || null,
    loading,
    error
  }
}
