import { useState } from "react"

interface ToastProps {
  title: string
  description?: string
  variant?: "default" | "destructive"
}

interface Toast extends ToastProps {
  id: string
}

export function toast(props: ToastProps) {
  // 简单的toast实现，可以后续集成更完整的toast库
  console.log(`[${props.variant || 'default'}] ${props.title}: ${props.description || ''}`)
  
  // 在实际项目中，这里应该触发toast显示
  // 目前先用console.log代替
}

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = (props: ToastProps) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast = { ...props, id }
    setToasts(prev => [...prev, newToast])
    
    // 自动移除toast
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== id))
    }, 5000)
    
    return id
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }

  const success = (title: string, description?: string) => {
    return addToast({ title, description, variant: 'default' })
  }

  const error = (title: string, description?: string) => {
    return addToast({ title, description, variant: 'destructive' })
  }

  return {
    toasts,
    toast: addToast,
    success,
    error,
    dismiss: removeToast
  }
}
