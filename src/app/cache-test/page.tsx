"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { toast } from "sonner"
import { 
  invalidateNamespaceVersionCache,
  invalidateReleaseListCache,
  smartCacheInvalidation 
} from "@/lib/cache-invalidation"

export default function CacheTestPage() {
  const [namespace, setNamespace] = useState("common")
  const [loading, setLoading] = useState(false)

  const handleInvalidateAll = async () => {
    setLoading(true)
    try {
      const result = await invalidateAllCache()
      if (result.success) {
        toast.success("所有缓存清理成功")
      } else {
        toast.error("缓存清理失败: " + result.message)
      }
    } catch (error) {
      toast.error("缓存清理失败")
    } finally {
      setLoading(false)
    }
  }

  const handleInvalidateTranslation = async () => {
    if (!namespace) {
      toast.error("请输入命名空间名称")
      return
    }
    
    setLoading(true)
    try {
      const result = await invalidateTranslationCache(namespace)
      if (result.success) {
        toast.success(`命名空间 '${namespace}' 的翻译缓存清理成功`)
      } else {
        toast.error("翻译缓存清理失败: " + result.message)
      }
    } catch (error) {
      toast.error("翻译缓存清理失败")
    } finally {
      setLoading(false)
    }
  }

  const handleInvalidateVersion = async () => {
    if (!namespace) {
      toast.error("请输入命名空间名称")
      return
    }
    
    setLoading(true)
    try {
      const result = await invalidateVersionCache(namespace)
      if (result.success) {
        toast.success(`命名空间 '${namespace}' 的版本缓存清理成功`)
      } else {
        toast.error("版本缓存清理失败: " + result.message)
      }
    } catch (error) {
      toast.error("版本缓存清理失败")
    } finally {
      setLoading(false)
    }
  }

  const handleSmartInvalidation = async () => {
    if (!namespace) {
      toast.error("请输入命名空间名称")
      return
    }
    
    setLoading(true)
    try {
      await smartCacheInvalidation(
        'translation',
        namespace
      )
      toast.success(`智能缓存清理完成 (命名空间: ${namespace})`)
    } catch (error) {
      toast.error("智能缓存清理失败")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Go服务缓存失效测试</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>全局缓存操作</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={handleInvalidateAll}
              disabled={loading}
              className="w-full"
              variant="destructive"
            >
              清空所有缓存
            </Button>
            <p className="text-sm text-muted-foreground">
              清空Go服务中的所有缓存，包括翻译、版本、命名空间等所有数据的缓存。
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>命名空间特定缓存操作</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="输入命名空间名称 (如: common)"
              value={namespace}
              onChange={(e) => setNamespace(e.target.value)}
            />
            
            <div className="grid gap-2">
              <Button 
                onClick={handleInvalidateTranslation}
                disabled={loading || !namespace}
                className="w-full"
              >
                清空翻译缓存
              </Button>
              
              <Button 
                onClick={handleInvalidateVersion}
                disabled={loading || !namespace}
                className="w-full"
              >
                清空版本缓存
              </Button>
              
              <Button 
                onClick={handleSmartInvalidation}
                disabled={loading || !namespace}
                className="w-full"
                variant="outline"
              >
                智能缓存清理
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground">
              针对特定命名空间清理相关缓存。智能缓存清理会根据操作类型自动选择最佳的清理策略。
            </p>
          </CardContent>
        </Card>
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>测试步骤：</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>确保Go i18n服务正在运行 (localhost:8080)</li>
              <li>在前端修改翻译数据或版本信息</li>
              <li>使用上述按钮手动触发缓存清理</li>
              <li>验证Go服务的翻译查询是否返回最新数据</li>
            </ol>
            
            <p className="mt-4"><strong>自动缓存失效：</strong></p>
            <p>在正常使用中，以下操作会自动触发缓存失效：</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>创建、更新翻译键</li>
              <li>创建命名空间</li>
              <li>创建、发布版本</li>
              <li>其他数据修改操作</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
