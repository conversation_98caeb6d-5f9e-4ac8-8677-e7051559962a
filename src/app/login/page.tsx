"use client"

import { useState } from "react"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

export default function LoginPage() {
  const [loginForm, setLoginForm] = useState({ username: "", password: "" })
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // 使用LDAP认证提供商
      const result = await signIn("ldap", {
        username: loginForm.username,
        password: loginForm.password,
        redirect: false,
      })

      if (result?.error) {
        if (result.error === "CredentialsSignin") {
          toast.error("用户名或密码错误，请检查LDAP凭据")
        } else if (result.error === "AccessDenied") {
          toast.error("访问被拒绝，请联系管理员")
        } else {
          toast.error("登录失败，请重试或联系IT支持")
        }
      } else {
        toast.success("登录成功")
        router.push("/")
      }
    } catch (error) {
      toast.error("LDAP认证服务连接失败，请联系IT支持")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl text-center">i18n 管理系统</CardTitle>
          <CardDescription className="text-center">
            使用企业LDAP账号登录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                type="text"
                placeholder="请输入LDAP用户名"
                value={loginForm.username}
                onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                placeholder="请输入LDAP密码"
                value={loginForm.password}
                onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "登录中..." : "LDAP登录"}
            </Button>
            
            <div className="text-sm text-gray-600 text-center mt-4">
              <p>使用您的企业域账号登录</p>
              <p>如有问题请联系IT支持</p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 