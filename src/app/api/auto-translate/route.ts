import { NextRequest, NextResponse } from "next/server"
import { Translate } from '@google-cloud/translate/build/src/v2'
import AWS from 'aws-sdk'

// 通用语言映射表 - 将我们的语言代码映射到翻译服务支持的代码
const languageMap: Record<string, string> = {
  'en_US': 'en',     // 英语
  'zh_CN': 'zh',     // 简体中文
  'zh_TW': 'zh',     // 繁体中文（大多数服务不区分简繁体）
  'ja_JP': 'ja',     // 日语
  'ko_KR': 'ko',     // 韩语
  'fr_FR': 'fr',     // 法语
  'de_DE': 'de',     // 德语
  'es_ES': 'es',     // 西班牙语
  'pt_PT': 'pt',     // 葡萄牙语
  'ru_RU': 'ru',     // 俄语
  'ar_SA': 'ar',     // 阿拉伯语
  'bn_BD': 'bn',     // 孟加拉语
  'hi_IN': 'hi',     // 印地语
  'th_TH': 'th',     // 泰语
  'vi_VN': 'vi',     // 越南语
  'id_ID': 'id',     // 印尼语
  'tr_TR': 'tr',     // 土耳其语
  'ur_PK': 'ur',     // 乌尔都语
  'fa_IR': 'fa',     // 波斯语
}

// AWS Translate 特殊语言映射（不支持下划线格式）
const awsLanguageMap: Record<string, string> = {
  'en_US': 'en',     // 英语
  'zh_CN': 'zh',     // 简体中文
  'zh_TW': 'zh',     // 繁体中文（大多数服务不区分简繁体）
  'ja_JP': 'ja',     // 日语
  'ko_KR': 'ko',     // 韩语
  'fr_FR': 'fr',     // 法语
  'de_DE': 'de',     // 德语
  'es_ES': 'es',     // 西班牙语
  'pt_PT': 'pt',     // 葡萄牙语
  'ru_RU': 'ru',     // 俄语
  'ar_SA': 'ar',     // 阿拉伯语
  'bn_BD': 'bn',     // 孟加拉语
  'hi_IN': 'hi',     // 印地语
  'th_TH': 'th',     // 泰语
  'vi_VN': 'vi',     // 越南语
  'id_ID': 'id',     // 印尼语
  'tr_TR': 'tr',     // 土耳其语
  'ur_PK': 'ur',     // 乌尔都语
  'fa_IR': 'fa',     // 波斯语
}

// 获取适合的语言代码
function getLanguageCode(lang: string, service: 'google' | 'aws' | 'free'): string {
  switch (service) {
    case 'aws':
      return awsLanguageMap[lang] || languageMap[lang] || lang
    case 'google':
    case 'free':
    default:
      return languageMap[lang] || lang
  }
}

// Google Translate 客户端初始化
let googleTranslate: Translate | null = null
if (process.env.GOOGLE_TRANSLATE_API_KEY) {
  googleTranslate = new Translate({
    key: process.env.GOOGLE_TRANSLATE_API_KEY,
  })
}

// AWS Translate 客户端初始化
let awsTranslate: AWS.Translate | null = null
if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'us-east-1'
  })
  awsTranslate = new AWS.Translate()
}

// ChatGPT 单语言翻译（OpenAI GPT-4）
async function translateWithChatGPT(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  if (!process.env.OPENAI_API_KEY) {
    return null
  }

  try {
    const sourceLanguageName = getLanguageName(sourceLang)
    const targetLanguageName = getLanguageName(targetLang)
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini', // 使用更经济的模型
        messages: [
          {
            role: 'system',
            content: `You are a professional translator. Translate the given text from ${sourceLanguageName} to ${targetLanguageName}. Only return the translated text without any explanations or additional content.`
          },
          {
            role: 'user',
            content: text
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`ChatGPT HTTP错误: ${response.status}`)
      return null
    }

    const data = await response.json()
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      return data.choices[0].message.content.trim()
    }
    
    return null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('ChatGPT 请求超时')
    } else {
      console.error('ChatGPT 错误:', error)
    }
    return null
  }
}

// ChatGPT 批量翻译（一次API调用处理多种语言）
async function batchTranslateWithChatGPT(text: string, targetLangs: string[], sourceLang: string = 'en'): Promise<Record<string, string> | null> {
  if (!process.env.OPENAI_API_KEY) {
    return null
  }

  try {
    const sourceLanguageName = getLanguageName(sourceLang)
    const targetLanguageNames = targetLangs.map(lang => getLanguageName(lang))
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时，批量翻译需要更多时间

    const languageList = targetLangs.map((lang, index) => `${lang}: ${targetLanguageNames[index]}`).join('\n')

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are a professional translator. Translate the given text from ${sourceLanguageName} to multiple languages. Return the translations in JSON format with language codes as keys. Only return valid JSON without any explanations.

Target languages:
${languageList}

Example response format:
{
  "zh_CN": "翻译文本",
  "ja_JP": "翻訳テキスト",
  "fr_FR": "texte traduit"
}`
          },
          {
            role: 'user',
            content: text
          }
        ],
        max_tokens: 2000, // 增加token限制以支持多语言
        temperature: 0.3
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`ChatGPT 批量翻译HTTP错误: ${response.status}`)
      return null
    }

    const data = await response.json()
    
    if (data.choices && data.choices[0] && data.choices[0].message) {
      try {
        const translations = JSON.parse(data.choices[0].message.content.trim())
        return translations
      } catch (parseError) {
        console.error('ChatGPT 批量翻译JSON解析错误:', parseError)
        return null
      }
    }
    
    return null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('ChatGPT 批量翻译请求超时')
    } else {
      console.error('ChatGPT 批量翻译错误:', error)
    }
    return null
  }
}

// Gemini 单语言翻译（Google Gemini）
async function translateWithGemini(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  if (!process.env.GEMINI_API_KEY) {
    return null
  }

  try {
    const sourceLanguageName = getLanguageName(sourceLang)
    const targetLanguageName = getLanguageName(targetLang)
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `Translate the following text from ${sourceLanguageName} to ${targetLanguageName}. Only return the translated text without any explanations:\n\n${text}`
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 1000,
        }
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`Gemini HTTP错误: ${response.status}`)
      return null
    }

    const data = await response.json()
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
      return data.candidates[0].content.parts[0].text.trim()
    }
    
    return null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('Gemini 请求超时')
    } else {
      console.error('Gemini 错误:', error)
    }
    return null
  }
}

// Gemini 批量翻译（一次API调用处理多种语言）
async function batchTranslateWithGemini(text: string, targetLangs: string[], sourceLang: string = 'en'): Promise<Record<string, string> | null> {
  if (!process.env.GEMINI_API_KEY) {
    return null
  }

  try {
    const sourceLanguageName = getLanguageName(sourceLang)
    const targetLanguageNames = targetLangs.map(lang => getLanguageName(lang))
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时

    const languageList = targetLangs.map((lang, index) => `${lang}: ${targetLanguageNames[index]}`).join('\n')

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `Translate the following text from ${sourceLanguageName} to multiple languages. Return the translations in JSON format with language codes as keys. Only return valid JSON without any explanations.

Target languages:
${languageList}

Example response format:
{
  "zh_CN": "翻译文本",
  "ja_JP": "翻訳テキスト", 
  "fr_FR": "texte traduit"
}

Text to translate: ${text}`
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 2000,
        }
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`Gemini 批量翻译HTTP错误: ${response.status}`)
      return null
    }

    const data = await response.json()
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts) {
      try {
        const responseText = data.candidates[0].content.parts[0].text.trim()
        // 尝试提取JSON部分（有时Gemini会包含额外文本）
        const jsonMatch = responseText.match(/\{[\s\S]*\}/)
        const jsonText = jsonMatch ? jsonMatch[0] : responseText
        const translations = JSON.parse(jsonText)
        return translations
      } catch (parseError) {
        console.error('Gemini 批量翻译JSON解析错误:', parseError)
        return null
      }
    }
    
    return null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('Gemini 批量翻译请求超时')
    } else {
      console.error('Gemini 批量翻译错误:', error)
    }
    return null
  }
}

// 获取语言的完整名称
function getLanguageName(langCode: string): string {
  const languageNames: Record<string, string> = {
    'en_US': 'English',
    'zh_CN': 'Chinese (Simplified)',
    'zh_TW': 'Chinese (Traditional)',
    'ja_JP': 'Japanese',
    'ko_KR': 'Korean',
    'fr_FR': 'French',
    'de_DE': 'German',
    'es_ES': 'Spanish',
    'pt_PT': 'Portuguese',
    'ru_RU': 'Russian',
    'ar_SA': 'Arabic',
    'bn_BD': 'Bengali',
    'hi_IN': 'Hindi',
    'th_TH': 'Thai',
    'vi_VN': 'Vietnamese',
    'id_ID': 'Indonesian',
    'tr_TR': 'Turkish',
    'ur_PK': 'Urdu',
    'fa_IR': 'Persian'
  }
  return languageNames[langCode] || langCode
}

// Google Translate 翻译（高质量付费服务）
async function translateWithGoogle(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  if (!googleTranslate) {
    return null
  }

  try {
    const sourceCode = getLanguageCode(sourceLang, 'google')
    const targetCode = getLanguageCode(targetLang, 'google')

    const [translation] = await googleTranslate.translate(text, {
      from: sourceCode,
      to: targetCode,
    })
    
    return translation || null
  } catch (error) {
    console.error('Google Translate 错误:', error)
    return null
  }
}

// AWS Translate 翻译（高质量付费服务）
async function translateWithAWS(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  if (!awsTranslate) {
    return null
  }

  try {
    const sourceCode = getLanguageCode(sourceLang, 'aws')
    const targetCode = getLanguageCode(targetLang, 'aws')

    const params = {
      Text: text,
      SourceLanguageCode: sourceCode,
      TargetLanguageCode: targetCode,
    }
    
    const result = await awsTranslate.translateText(params).promise()
    return result.TranslatedText || null
  } catch (error) {
    console.error('AWS Translate 错误:', error)
    return null
  }
}

// LibreTranslate 翻译（免费开源服务）
async function translateWithLibreTranslate(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  try {
    const sourceCode = getLanguageCode(sourceLang, 'free')
    const targetCode = getLanguageCode(targetLang, 'free')

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch('https://libretranslate.de/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: text,
        source: sourceCode,
        target: targetCode,
        format: 'text'
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`LibreTranslate HTTP错误: ${response.status}`)
      return null
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      console.error('LibreTranslate 返回非JSON响应')
      return null
    }

    const data = await response.json()
    return data.translatedText || null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('LibreTranslate 请求超时')
    } else {
      console.error('LibreTranslate 错误:', error)
    }
    return null
  }
}

// MyMemory 翻译（免费服务，每天1000次调用）
async function translateWithMyMemory(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  try {
    const sourceCode = languageMap[sourceLang] || sourceLang
    const targetCode = languageMap[targetLang] || targetLang
    
    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 8000) // 8秒超时
    
    const response = await fetch(
      `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${sourceCode}|${targetCode}`,
      { signal: controller.signal }
    )

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`MyMemory HTTP错误: ${response.status}`)
      return null
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      console.error('MyMemory 返回非JSON响应')
      return null
    }

    const data = await response.json()
    
    if (data.responseStatus === 200 && data.responseData) {
      return data.responseData.translatedText || null
    }
    
    // 检查是否超出限制
    if (data.responseStatus === 403) {
      console.error('MyMemory 超出每日调用限制')
    }
    
    return null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('MyMemory 请求超时')
    } else {
      console.error('MyMemory 错误:', error)
    }
    return null
  }
}

// Lingva Translate 翻译（免费开源服务，更可靠）
async function translateWithLingva(text: string, targetLang: string, sourceLang: string = 'en'): Promise<string | null> {
  try {
    const sourceCode = languageMap[sourceLang] || sourceLang
    const targetCode = languageMap[targetLang] || targetLang
    
    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 8000) // 8秒超时
    
    const response = await fetch(
      `https://lingva.ml/api/v1/${sourceCode}/${targetCode}/${encodeURIComponent(text)}`,
      { signal: controller.signal }
    )

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`Lingva HTTP错误: ${response.status}`)
      return null
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Lingva 返回非JSON响应')
      return null
    }

    const data = await response.json()
    return data.translation || null
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.error('Lingva 请求超时')
    } else {
      console.error('Lingva 错误:', error)
    }
    return null
  }
}

// 批量翻译函数 - 优化API调用次数
async function batchTranslateText(text: string, targetLangs: string[], sourceLang: string = 'en_US'): Promise<Record<string, string>> {
  const results: Record<string, string> = {}

  // 优先级 1：ChatGPT 批量翻译（最高质量，单次API调用）
  const chatgptResults = await batchTranslateWithChatGPT(text, targetLangs, sourceLang)
  if (chatgptResults) {
    console.log('使用 ChatGPT 批量翻译成功')
    Object.assign(results, chatgptResults)
    // 检查是否所有语言都翻译成功
    const remainingLangs = targetLangs.filter(lang => !results[lang])
    if (remainingLangs.length === 0) {
      return results
    }
    targetLangs = remainingLangs // 只翻译剩余的语言
  }

  // 优先级 2：Gemini 批量翻译（高质量备用，单次API调用）
  const geminiResults = await batchTranslateWithGemini(text, targetLangs, sourceLang)
  if (geminiResults) {
    console.log('使用 Gemini 批量翻译成功')
    Object.assign(results, geminiResults)
    // 检查是否所有语言都翻译成功
    const remainingLangs = targetLangs.filter(lang => !results[lang])
    if (remainingLangs.length === 0) {
      return results
    }
    targetLangs = remainingLangs // 只翻译剩余的语言
  }

  // 如果AI批量翻译失败，降级到单个翻译服务
  for (const targetLang of targetLangs) {
    if (!results[targetLang]) {
      const translation = await translateText(text, targetLang, sourceLang)
      if (translation) {
        results[targetLang] = translation
      } else {
        results[targetLang] = `[翻译失败] ${text}`
      }
    }
  }

  return results
}

// 单语言翻译函数 - 按优先级尝试不同的翻译服务
async function translateText(text: string, targetLang: string, sourceLang: string = 'en_US'): Promise<string | null> {
  let result: string | null = null

  // 优先级 1：ChatGPT（最高质量AI翻译）
  result = await translateWithChatGPT(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 ChatGPT 翻译成功')
    return result
  }

  // 优先级 2：Gemini（高质量AI翻译备用）
  result = await translateWithGemini(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 Gemini 翻译成功')
    return result
  }

  // 优先级 3：Google Translate（传统高质量付费服务）
  result = await translateWithGoogle(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 Google Translate 翻译成功')
    return result
  }

  // 优先级 4：AWS Translate（传统高质量备用）
  result = await translateWithAWS(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 AWS Translate 翻译成功')
    return result
  }

  // 优先级 5：Lingva Translate（免费开源，更可靠）
  result = await translateWithLingva(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 Lingva Translate 翻译成功')
    return result
  }

  // 优先级 6：MyMemory（免费备用）
  result = await translateWithMyMemory(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 MyMemory 翻译成功')
    return result
  }

  // 优先级 7：LibreTranslate（最后备用）
  result = await translateWithLibreTranslate(text, targetLang, sourceLang)
  if (result) {
    console.log('使用 LibreTranslate 翻译成功')
    return result
  }

  // 所有服务都失败
  console.log('所有翻译服务都失败')
  return null
}

export async function POST(request: NextRequest) {
  try {
    const { sourceText, sourceLanguage = 'en_US', targetLanguages } = await request.json()

    if (!sourceText || !targetLanguages || !Array.isArray(targetLanguages)) {
      return NextResponse.json(
        { error: "缺少必要参数: sourceText, targetLanguages" },
        { status: 400 }
      )
    }

    // 过滤掉源语言，避免重复翻译
    const filteredTargetLanguages = targetLanguages.filter(lang => lang !== sourceLanguage)
    
    let translations: Record<string, string> = {}

    // 如果有多个目标语言，使用批量翻译（优化API调用）
    if (filteredTargetLanguages.length > 1) {
      console.log(`批量翻译模式: ${filteredTargetLanguages.length} 种语言`)
      translations = await batchTranslateText(sourceText, filteredTargetLanguages, sourceLanguage)
    } else if (filteredTargetLanguages.length === 1) {
      // 单个语言翻译
      console.log(`单语言翻译模式: ${filteredTargetLanguages[0]}`)
      const targetLang = filteredTargetLanguages[0]
      const translated = await translateText(sourceText, targetLang, sourceLanguage)
      if (translated) {
        translations[targetLang] = translated
      } else {
        translations[targetLang] = `[翻译失败] ${sourceText}`
      }
    }

    // 添加源语言本身
    if (targetLanguages.includes(sourceLanguage)) {
      translations[sourceLanguage] = sourceText
    }

    return NextResponse.json({
      success: true,
      translations,
      sourceText,
      sourceLanguage
    })

  } catch (error) {
    console.error("自动翻译错误:", error)
    return NextResponse.json(
      { error: "自动翻译失败" },
      { status: 500 }
    )
  }
} 