import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { smartCacheInvalidation } from "@/lib/cache-invalidation"

// GET /api/namespaces - 获取命名空间列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    
    // 分页参数
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100)
    const offset = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { displayName: { contains: search } }
      ]
    }

    // 并行查询
    const [namespaces, total] = await Promise.all([
      prisma.namespace.findMany({
        where,
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          createdAt: true,
          updatedAt: true,
          creator: {
            select: {
              id: true,
              username: true
            }
          },
          _count: {
            select: {
              keys: true
            }
          }
        },
        skip: offset,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.namespace.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      data: namespaces,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error("获取命名空间列表失败:", error)
    return NextResponse.json({ error: "获取命名空间列表失败" }, { status: 500 })
  }
}

// POST /api/namespaces - 创建命名空间
export async function POST(request: NextRequest) {
  try {
    const { name, displayName, description, createdBy } = await request.json()

    if (!name || !displayName || !createdBy) {
      return NextResponse.json({ error: "名称、显示名称和创建者ID是必填项" }, { status: 400 })
    }

    // 验证创建者用户是否存在
    const creator = await prisma.user.findUnique({
      where: { id: createdBy }
    })

    if (!creator) {
      return NextResponse.json({ 
        error: "创建者用户不存在", 
        details: `用户ID ${createdBy} 在数据库中不存在` 
      }, { status: 400 })
    }

    // 检查命名空间名称是否已存在（全局唯一）
    const existingNamespace = await prisma.namespace.findUnique({
      where: {
        name
      }
    })

    if (existingNamespace) {
      return NextResponse.json({ error: "命名空间名称已存在" }, { status: 400 })
    }

    const namespace = await prisma.namespace.create({
      data: {
        name,
        displayName,
        description,
        createdBy
      },
      include: {
        _count: {
          select: {
            keys: true
          }
        }
      }
    })

    // 异步清理Go服务缓存（不阻塞响应）
    smartCacheInvalidation(
      'namespace',
      namespace.name
    ).catch(error => {
      console.error('Cache invalidation failed after namespace creation:', error)
    })

    return NextResponse.json(namespace)
  } catch (error) {
    console.error("创建命名空间失败:", error)
    return NextResponse.json({ error: "创建命名空间失败" }, { status: 500 })
  }
}
