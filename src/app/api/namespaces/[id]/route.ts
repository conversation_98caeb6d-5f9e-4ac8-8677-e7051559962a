import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// DELETE /api/namespaces/[id] - 删除命名空间
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查命名空间是否存在
    const namespace = await prisma.namespace.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            keys: true
          }
        }
      }
    })

    if (!namespace) {
      return NextResponse.json({ error: "命名空间不存在" }, { status: 404 })
    }

    // 检查是否有关联的翻译键
    if (namespace._count.keys > 0) {
      return NextResponse.json({ 
        error: "无法删除包含翻译键的命名空间，请先删除所有相关翻译键" 
      }, { status: 400 })
    }

    // 删除命名空间
    await prisma.namespace.delete({
      where: { id }
    })

    return NextResponse.json({ message: "命名空间删除成功" })
  } catch (error) {
    console.error("删除命名空间失败:", error)
    return NextResponse.json({ error: "删除命名空间失败" }, { status: 500 })
  }
}

// GET /api/namespaces/[id] - 获取单个命名空间详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const namespace = await prisma.namespace.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            keys: true
          }
        }
      }
    })

    if (!namespace) {
      return NextResponse.json({ error: "命名空间不存在" }, { status: 404 })
    }

    return NextResponse.json(namespace)
  } catch (error) {
    console.error("获取命名空间详情失败:", error)
    return NextResponse.json({ error: "获取命名空间详情失败" }, { status: 500 })
  }
}

// PUT /api/namespaces/[id] - 更新命名空间
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { name, displayName, description } = await request.json()

    if (!name || !displayName) {
      return NextResponse.json({ error: "名称和显示名称是必填项" }, { status: 400 })
    }

    // 检查命名空间是否存在
    const existingNamespace = await prisma.namespace.findUnique({
      where: { id }
    })

    if (!existingNamespace) {
      return NextResponse.json({ error: "命名空间不存在" }, { status: 404 })
    }

    // 如果名称发生变化，检查是否有重名
    if (name !== existingNamespace.name) {
      const duplicateNamespace = await prisma.namespace.findUnique({
        where: { name }
      })

      if (duplicateNamespace) {
        return NextResponse.json({ error: "命名空间名称已存在" }, { status: 400 })
      }
    }

    const updatedNamespace = await prisma.namespace.update({
      where: { id },
      data: {
        name,
        displayName,
        description
      },
      include: {
        _count: {
          select: {
            keys: true
          }
        }
      }
    })

    return NextResponse.json(updatedNamespace)
  } catch (error) {
    console.error("更新命名空间失败:", error)
    return NextResponse.json({ error: "更新命名空间失败" }, { status: 500 })
  }
}
