import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET - 获取用户列表
export async function GET() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    return NextResponse.json(users)
  } catch (error) {
    console.error("获取用户列表错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
