import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "../auth/[...nextauth]/route"

// 语言映射 - 从完整地区代码映射到翻译服务的语言代码
const languageMap: Record<string, string> = {
  zh_CN: "zh",
  zh_TW: "zh-TW", 
  en_US: "en",
  ja_JP: "ja",
  ko_KR: "ko",
  fr_FR: "fr",
  de_DE: "de",
  es_ES: "es",
  pt_PT: "pt",
  ru_RU: "ru",
  ar_SA: "ar",
  bn_BD: "bn",
  hi_IN: "hi",
  th_TH: "th",
  vi_VN: "vi",
  id_ID: "id",
  tr_TR: "tr",
  ur_PK: "ur",
  fa_IR: "fa"
}

// 模拟翻译函数 - 在实际项目中，可以集成Google Translate, DeepL等服务
async function translateText(text: string, targetLanguage: string): Promise<string> {
  // 这里使用简单的模拟翻译
  // 实际项目中可以集成真实的翻译API
  const translations: Record<string, Record<string, string>> = {
    "Hello": {
      zh: "你好",
      "zh-TW": "你好",
      ja: "こんにちは",
      ko: "안녕하세요",
      fr: "Bonjour",
      de: "Hallo",
      es: "Hola",
      pt: "Olá",
      ru: "Привет",
      ar: "مرحبا",
      bn: "হ্যালো",
      hi: "नमस्ते",
      th: "สวัสดี",
      vi: "Xin chào",
      id: "Halo",
      tr: "Merhaba",
      ur: "ہیلو",
      it: "Ciao"
    },
    "Welcome": {
      zh: "欢迎",
      "zh-TW": "歡迎",
      ja: "ようこそ",
      ko: "환영합니다",
      fr: "Bienvenue",
      de: "Willkommen",
      es: "Bienvenido",
      pt: "Bem-vindo",
      ru: "Добро пожаловать",
      ar: "مرحبا بك",
      bn: "স্বাগতম",
      hi: "स्वागत",
      th: "ยินดีต้อนรับ",
      vi: "Chào mừng",
      id: "Selamat datang",
      tr: "Hoş geldiniz",
      ur: "خوش آمدید",
      it: "Benvenuto"
    },
    "Submit": {
      zh: "提交",
      "zh-TW": "提交",
      ja: "送信",
      ko: "제출",
      fr: "Soumettre",
      de: "Einreichen",
      es: "Enviar",
      pt: "Enviar",
      ru: "Отправить",
      ar: "إرسال",
      bn: "জমা দিন",
      hi: "जमा करें",
      th: "ส่ง",
      vi: "Gửi",
      id: "Kirim",
      tr: "Gönder",
      ur: "جمع کریں",
      it: "Invia"
    },
    "Cancel": {
      zh: "取消",
      "zh-TW": "取消",
      ja: "キャンセル",
      ko: "취소",
      fr: "Annuler",
      de: "Abbrechen",
      es: "Cancelar",
      pt: "Cancelar",
      ru: "Отмена",
      ar: "إلغاء",
      bn: "বাতিল",
      hi: "रद्द करें",
      th: "ยกเลิก",
      vi: "Hủy",
      id: "Batal",
      tr: "İptal",
      ur: "منسوخ",
      it: "Annulla"
    }
  }

  // 简单的关键词匹配翻译
  const baseText = text.trim()
  if (translations[baseText] && translations[baseText][targetLanguage]) {
    return translations[baseText][targetLanguage]
  }

  // 如果没有找到精确匹配，返回带标识的文本
  return `[${targetLanguage}] ${text}`
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "未登录" },
        { status: 401 }
      )
    }

    const { text, targetLanguages } = await request.json()

    if (!text || !Array.isArray(targetLanguages) || targetLanguages.length === 0) {
      return NextResponse.json(
        { error: "文本和目标语言列表是必填项" },
        { status: 400 }
      )
    }

    const translations: Record<string, string> = {}

    for (const lang of targetLanguages) {
      const targetLang = languageMap[lang]
      if (targetLang) {
        try {
          const translation = await translateText(text, targetLang)
          translations[lang] = translation
        } catch (error) {
          console.error(`翻译到 ${lang} 失败:`, error)
          translations[lang] = `[翻译失败] ${text}`
        }
      } else {
        translations[lang] = `[不支持的语言] ${text}`
      }
    }

    return NextResponse.json({
      success: true,
      translations
    })
  } catch (error) {
    console.error("自动翻译失败:", error)
    return NextResponse.json(
      { error: "自动翻译失败" },
      { status: 500 }
    )
  }
} 