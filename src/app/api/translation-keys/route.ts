import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { smartCacheInvalidation } from "@/lib/cache-invalidation"

// 获取翻译键列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const namespaceId = searchParams.get('namespaceId')
    const releaseId = searchParams.get('releaseId')
    const search = searchParams.get('search')
    
    // 分页参数
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100) // 最大100条
    const offset = (page - 1) * limit
    
    // 语言选择参数
    const languages = searchParams.get('languages')?.split(',') || ['zh_CN', 'en_US']
    
    const where: any = {}

    if (namespaceId) {
      where.namespaceId = namespaceId
    }

    if (releaseId) {
      if (releaseId === 'unassigned') {
        where.releaseId = null
      } else {
        where.releaseId = releaseId
      }
    }

    if (search) {
      where.OR = [
        { key: { contains: search } },
        { description: { contains: search } }
      ]
    }

    // 构建选择字段 - 只查询需要的语言字段
    const languageFields = languages.reduce((acc, lang) => {
      acc[lang] = true
      return acc
    }, {} as Record<string, boolean>)

    // 并行查询总数和数据
    const [translationKeys, total] = await Promise.all([
      prisma.translationKey.findMany({
        where,
        select: {
          id: true,
          key: true,
          description: true,
          createdAt: true,
          updatedAt: true,
          ...languageFields,
          namespace: {
            select: {
              id: true,
              name: true,
              displayName: true
            }
          },
          creator: {
            select: {
              id: true,
              username: true
            }
          },
          release: {
            select: {
              id: true,
              version: true,
              status: true
            }
          }
        },
        skip: offset,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.translationKey.count({ where })
    ])

    // 返回分页信息
    const totalPages = Math.ceil(total / limit)
    
    return NextResponse.json({
      data: translationKeys,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error("获取翻译键列表错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// 创建翻译键
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { key, namespaceId, description, releaseId, translations, createdBy } = body

    if (!key || !namespaceId) {
      return NextResponse.json(
        { error: "键名和命名空间ID都是必填项" },
        { status: 400 }
      )
    }

    // 获取当前用户ID
    if (!createdBy) {
      // 从请求头或会话中获取当前用户
      // 这里应该从认证中间件或JWT token中获取用户ID
      // 暂时返回错误，要求前端提供用户ID
      return NextResponse.json(
        { error: "缺少用户身份信息，请重新登录" },
        { status: 401 }
      )
    }

    // 检查命名空间是否存在
    const namespace = await prisma.namespace.findUnique({
      where: { id: namespaceId }
    })

    if (!namespace) {
      return NextResponse.json(
        { error: "命名空间不存在" },
        { status: 404 }
      )
    }

    // 检查版本是否存在（如果指定了版本）
    let release = null
    if (releaseId) {
      release = await prisma.release.findUnique({
        where: { id: releaseId }
      })

      if (!release) {
        return NextResponse.json(
          { error: "版本不存在" },
          { status: 404 }
        )
      }

      // 只有草稿状态的版本才能添加翻译键
      if (release.status !== "DRAFT") {
        return NextResponse.json(
          { error: "只有草稿状态的版本才能添加翻译键" },
          { status: 400 }
        )
      }

      // 🚨 架构一致性校验：翻译键的命名空间必须与版本的命名空间一致
      if (release.namespaceId !== namespaceId) {
        return NextResponse.json(
          { error: `翻译键的命名空间(${namespace.name})与版本的命名空间不一致，无法分配到此版本` },
          { status: 400 }
        )
      }
    }

    // 检查翻译键是否已存在（在同一版本中）
    // 根据唯一约束 @@unique([key, namespaceId, releaseId])，同一版本中key必须唯一
    const existingKey = await prisma.translationKey.findFirst({
      where: {
        key,
        namespaceId,
        releaseId
      }
    })

    if (existingKey) {
      const versionInfo = releaseId ? `版本 ${release?.version}` : '未分配版本'
      return NextResponse.json(
        { error: `翻译键 "${key}" 在${versionInfo}中已存在` },
        { status: 409 }
      )
    }

    // 准备翻译数据
    const translationData: any = {
      key,
      namespaceId,
      description,
      releaseId,
      createdBy
    }

    // 添加所有语言的翻译（与Prisma schema保持一致）
    const languageFields = [
      'zh_CN', 'zh_TW', 'en_US', 'ja_JP', 'ko_KR', 'fr_FR', 'de_DE', 'es_ES', 
      'pt_PT', 'ru_RU', 'ar_SA', 'bn_BD', 'hi_IN', 'th_TH', 'vi_VN', 'id_ID', 'tr_TR', 'ur_PK', 'fa_IR'
    ]
    
    languageFields.forEach(lang => {
      if (translations && translations[lang]) {
        translationData[lang] = translations[lang]
      }
    })

    const translationKey = await prisma.translationKey.create({
      data: translationData,
      include: {
        namespace: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        },
        creator: {
          select: {
            username: true
          }
        }
      }
    })

    // 异步清理Go服务缓存（不阻塞响应）
    smartCacheInvalidation(
      'translation',
      namespace.name,
      release?.version  // 传递版本信息（如果有的话）
    ).catch(error => {
      console.error('Cache invalidation failed after translation key creation:', error)
    })

    return NextResponse.json(translationKey)

  } catch (error) {
    console.error("创建翻译键错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// 更新翻译键
export async function PUT(request: NextRequest) {
  try {

    const { id, translations, description } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: "翻译键ID是必填项" },
        { status: 400 }
      )
    }

    // 检查翻译键是否存在
    const existingKey = await prisma.translationKey.findUnique({
      where: { id }
    })

    if (!existingKey) {
      return NextResponse.json(
        { error: "翻译键不存在" },
        { status: 404 }
      )
    }

    // 准备更新数据
    const updateData: any = {}

    if (description !== undefined) {
      updateData.description = description
    }

    // 更新所有语言的翻译
    if (translations) {
      const languageFields = [
        'zh_CN', 'zh_TW', 'en_US', 'ja_JP', 'ko_KR', 'fr_FR', 'de_DE', 'es_ES', 
        'pt_PT', 'ru_RU', 'ar_SA', 'bn_BD', 'hi_IN', 'th_TH', 'vi_VN', 'id_ID', 'tr_TR', 'ur_PK', 'fa_IR'
      ]
      
      languageFields.forEach(lang => {
        if (translations[lang] !== undefined) {
          updateData[lang] = translations[lang] || null
        }
      })
    }

    const translationKey = await prisma.translationKey.update({
      where: { id },
      data: updateData,
      include: {
        namespace: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        },
        creator: {
          select: {
            username: true
          }
        },
        release: {
          select: {
            id: true,
            version: true,
            status: true
          }
        }
      }
    })

    // 异步清理Go服务缓存（不阻塞响应）
    smartCacheInvalidation(
      'translation',
      translationKey.namespace.name,
      translationKey.release?.version  // 传递版本信息（如果有的话）
    ).catch(error => {
      console.error('Cache invalidation failed after translation key update:', error)
    })

    return NextResponse.json(translationKey)

  } catch (error) {
    console.error("更新翻译键错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
} 