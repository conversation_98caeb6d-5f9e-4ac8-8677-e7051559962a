import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 更新翻译键（支持版本分配）
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // 检查翻译键是否存在
    const existingKey = await prisma.translationKey.findUnique({
      where: { id },
      include: {
        release: {
          select: {
            id: true,
            version: true,
            status: true
          }
        }
      }
    })

    if (!existingKey) {
      return NextResponse.json(
        { error: "翻译键不存在" },
        { status: 404 }
      )
    }

    // 检查是否为已发布版本的翻译键，已发布版本不可编辑
    if (existingKey.release && existingKey.release.status === "PUBLISHED") {
      return NextResponse.json(
        { error: `翻译键属于已发布版本 "${existingKey.release.version}"，不可编辑` },
        { status: 400 }
      )
    }

    // 提取允许更新的字段
    const {
      key,
      description,
      namespaceId,
      releaseId,
      translations,
      updatedBy
    } = body

    // 构建更新数据对象
    const updateData: any = {
      updatedAt: new Date()
    }

    // 添加基本字段
    if (key !== undefined) updateData.key = key
    if (description !== undefined) updateData.description = description
    if (namespaceId !== undefined) updateData.namespaceId = namespaceId
    if (releaseId !== undefined) updateData.releaseId = releaseId

    // 处理翻译字段 - 从translations对象中提取
    if (translations && typeof translations === 'object') {
      const languageCodes = [
        'zh_CN', 'zh_TW', 'en_US', 'ja_JP', 'ko_KR',
        'fr_FR', 'de_DE', 'es_ES', 'pt_PT', 'ru_RU',
        'ar_SA', 'bn_BD', 'hi_IN', 'th_TH', 'vi_VN',
        'id_ID', 'tr_TR', 'ur_PK', 'fa_IR'
      ]
      
      languageCodes.forEach(langCode => {
        if (translations[langCode] !== undefined) {
          updateData[langCode] = translations[langCode]
        }
      })
    }

    // 🚨 架构一致性校验：如果同时更新了namespaceId和releaseId，需要确保它们一致
    if (namespaceId !== undefined && releaseId !== undefined && releaseId !== null) {
      const release = await prisma.release.findUnique({
        where: { id: releaseId }
      })
      
      if (!release) {
        return NextResponse.json(
          { error: "指定的版本不存在" },
          { status: 404 }
        )
      }
      
      if (release.namespaceId !== namespaceId) {
        return NextResponse.json(
          { error: "翻译键的命名空间与版本的命名空间不一致，无法分配到此版本" },
          { status: 400 }
        )
      }
    }
    
    // 如果只更新了releaseId，需要确保与现有的namespaceId一致
    if (releaseId !== undefined && releaseId !== null && namespaceId === undefined) {
      const currentKey = await prisma.translationKey.findUnique({
        where: { id },
        select: { namespaceId: true }
      })
      
      if (currentKey) {
        const release = await prisma.release.findUnique({
          where: { id: releaseId }
        })
        
        if (!release) {
          return NextResponse.json(
            { error: "指定的版本不存在" },
            { status: 404 }
          )
        }
        
        if (release.namespaceId !== currentKey.namespaceId) {
          return NextResponse.json(
            { error: "无法将翻译键分配到不同命名空间的版本" },
            { status: 400 }
          )
        }
      }
    }

    // 更新翻译键
    const updatedKey = await prisma.translationKey.update({
      where: { id },
      data: updateData,
      include: {
        namespace: {
          select: { id: true, name: true, displayName: true }
        },
        release: {
          select: { id: true, version: true, status: true }
        },
        creator: {
          select: { id: true, username: true }
        }
      }
    })

    // 异步清理Go服务缓存（不阻塞响应）
    const { smartCacheInvalidation } = await import('@/lib/cache-invalidation')
    smartCacheInvalidation(
      'translation',
      updatedKey.namespace.name,
      updatedKey.release?.version  // 传递版本信息（如果有的话）
    ).catch(error => {
      console.error('Cache invalidation failed after translation key update:', error)
    })

    return NextResponse.json({
      message: "翻译键更新成功",
      data: updatedKey
    })

  } catch (error) {
    console.error("更新翻译键错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// 删除翻译键
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查翻译键是否存在
    const existingKey = await prisma.translationKey.findUnique({
      where: { id },
      include: {
        release: {
          select: {
            id: true,
            version: true,
            status: true
          }
        }
      }
    })

    if (!existingKey) {
      return NextResponse.json(
        { error: "翻译键不存在" },
        { status: 404 }
      )
    }

    // 检查是否为已发布版本的翻译键，已发布版本不可删除
    if (existingKey.release && existingKey.release.status === "PUBLISHED") {
      return NextResponse.json(
        { error: `翻译键属于已发布版本 "${existingKey.release.version}"，不可删除` },
        { status: 400 }
      )
    }

    // 删除翻译键
    await prisma.translationKey.delete({
      where: { id }
    })

    return NextResponse.json({ message: "翻译键删除成功" })

  } catch (error) {
    console.error("删除翻译键错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// 获取单个翻译键
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const translationKey = await prisma.translationKey.findUnique({
      where: { id },
      include: {
        namespace: {
          select: {
            id: true,
            name: true,
            displayName: true
          }
        },
        creator: {
          select: {
            username: true
          }
        }
      }
    })

    if (!translationKey) {
      return NextResponse.json(
        { error: "翻译键不存在" },
        { status: 404 }
      )
    }

    return NextResponse.json(translationKey)

  } catch (error) {
    console.error("获取翻译键错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
