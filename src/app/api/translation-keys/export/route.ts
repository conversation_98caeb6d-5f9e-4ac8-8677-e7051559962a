import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 导出翻译键
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const namespaceId = searchParams.get("namespaceId")
    const releaseId = searchParams.get("releaseId")
    const search = searchParams.get("search")
    const format = searchParams.get("format") || "json" // json, csv, xlsx

    // 构建查询条件
    const where: any = {}
    
    if (namespaceId && namespaceId !== "all") {
      where.namespaceId = namespaceId
    }
    
    if (releaseId && releaseId !== "all") {
      if (releaseId === "unassigned") {
        where.releaseId = null
      } else {
        where.releaseId = releaseId
      }
    }
    
    if (search) {
      where.OR = [
        { key: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // 获取翻译键数据
    const translationKeys = await prisma.translationKey.findMany({
      where,
      include: {
        namespace: {
          select: { id: true, name: true, displayName: true }
        },
        release: {
          select: { id: true, version: true, status: true }
        },
        creator: {
          select: { id: true, username: true }
        }
      },
      orderBy: [
        { namespace: { name: 'asc' } },
        { key: 'asc' }
      ]
    })

    // 语言列表
    const languages = [
      'zh_CN', 'zh_TW', 'en_US', 'ja_JP', 'ko_KR',
      'fr_FR', 'de_DE', 'es_ES', 'pt_PT', 'ru_RU',
      'ar_SA', 'bn_BD', 'hi_IN', 'th_TH', 'vi_VN',
      'id_ID', 'tr_TR', 'ur_PK', 'fa_IR'
    ]

    if (format === "json") {
      // JSON 格式导出
      const exportData = {
        exportInfo: {
          exportTime: new Date().toISOString(),
          totalKeys: translationKeys.length,
          filters: {
            namespace: namespaceId || "全部",
            release: releaseId || "全部",
            search: search || "无"
          }
        },
        translationKeys: translationKeys.map(key => ({
          key: key.key,
          description: key.description,
          namespace: key.namespace.name,
          namespaceDisplayName: key.namespace.displayName,
          release: key.release?.version || "未分配版本",
          releaseStatus: key.release?.status || "N/A",
          createdAt: key.createdAt,
          updatedAt: key.updatedAt,
          creator: key.creator.username,
          translations: languages.reduce((acc, lang) => {
            const translation = key[lang as keyof typeof key] as string
            if (translation) {
              acc[lang] = translation
            }
            return acc
          }, {} as Record<string, string>)
        }))
      }

      return NextResponse.json(exportData)
    } else if (format === "csv") {
      // CSV 格式导出
      const headers = [
        'Key', 'Description', 'Namespace', 'Release', 'Creator', 'Created At', 'Updated At',
        ...languages
      ]
      
      const csvRows = [
        headers.join(','),
        ...translationKeys.map(key => {
          const row = [
            `"${key.key}"`,
            `"${key.description || ''}"`,
            `"${key.namespace.displayName}"`,
            `"${key.release?.version || '未分配版本'}"`,
            `"${key.creator.username}"`,
            `"${new Date(key.createdAt).toLocaleDateString('zh-CN')}"`,
            `"${new Date(key.updatedAt).toLocaleDateString('zh-CN')}"`,
            ...languages.map(lang => {
              const translation = key[lang as keyof typeof key] as string
              return `"${translation || ''}"`
            })
          ]
          return row.join(',')
        })
      ]

      const csvContent = csvRows.join('\n')
      
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv; charset=utf-8',
          'Content-Disposition': `attachment; filename="translation-keys-${new Date().toISOString().slice(0, 10)}.csv"`
        }
      })
    }

    return NextResponse.json({ error: "不支持的导出格式" }, { status: 400 })

  } catch (error) {
    console.error("导出翻译键错误:", error)
    return NextResponse.json(
      { error: "导出失败" },
      { status: 500 }
    )
  }
}
