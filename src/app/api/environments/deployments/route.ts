import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // 获取所有环境及其当前部署状态
    const environments = await prisma.environment.findMany({
      include: {
        deployments: {
          where: {
            status: "SUCCESS"
          },
          orderBy: {
            deployedAt: "desc"
          },
          take: 1, // 只取最新的成功部署
          include: {
            release: {
              select: {
                id: true,
                version: true,
                status: true,
                createdAt: true
              }
            },
            deployer: {
              select: {
                id: true,
                username: true
              }
            }
          }
        }
      },
      orderBy: {
        name: "asc"
      }
    })

    // 格式化数据
    const environmentStatus = environments.map(env => ({
      id: env.id,
      name: env.name,
      displayName: env.displayName,
      description: env.description,
      currentDeployment: env.deployments[0] || null
    }))

    return NextResponse.json(environmentStatus)

  } catch (error) {
    console.error("获取环境部署状态错误:", error)
    return NextResponse.json(
      { error: "获取环境状态失败" },
      { status: 500 }
    )
  }
}
