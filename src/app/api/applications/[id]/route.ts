import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/applications/[id] - 获取应用详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const application = await prisma.application.findUnique({
      where: { id },
      include: {
        environments: {
          include: {
            currentRelease: true,
            releases: {
              orderBy: { createdAt: 'desc' },
              take: 5
            }
          }
        },
        namespaces: {
          include: {
            _count: {
              select: {
                keys: true
              }
            }
          }
        }
      }
    })

    if (!application) {
      return NextResponse.json({ error: "应用不存在" }, { status: 404 })
    }

    return NextResponse.json(application)
  } catch (error) {
    console.error("获取应用详情失败:", error)
    return NextResponse.json({ error: "获取应用详情失败" }, { status: 500 })
  }
}

// PUT /api/applications/[id] - 更新应用
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { displayName, description } = await request.json()

    const application = await prisma.application.update({
      where: { id: params.id },
      data: {
        displayName,
        description
      }
    })

    return NextResponse.json(application)
  } catch (error) {
    console.error("更新应用失败:", error)
    return NextResponse.json({ error: "更新应用失败" }, { status: 500 })
  }
}

// DELETE /api/applications/[id] - 删除应用
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 删除应用会级联删除所有相关数据
    await prisma.application.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "应用删除成功" })
  } catch (error) {
    console.error("删除应用失败:", error)
    return NextResponse.json({ error: "删除应用失败" }, { status: 500 })
  }
}
