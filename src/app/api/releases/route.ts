import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { smartCacheInvalidation } from "@/lib/cache-invalidation"

// GET - 获取版本列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const namespaceId = searchParams.get('namespaceId')
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}
    if (namespaceId) where.namespaceId = namespaceId
    if (status) where.status = status
    if (search) {
      where.OR = [
        { version: { contains: search } },
        { description: { contains: search } }
      ]
    }

    const [releases, total] = await Promise.all([
      prisma.release.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          namespace: {
            select: { id: true, name: true, displayName: true }
          },
          creator: {
            select: { id: true, username: true }
          },
          basedOn: {
            select: { id: true, version: true }
          },
          _count: {
            select: { translationKeys: true }
          }
        }
      }),
      prisma.release.count({ where })
    ])

    return NextResponse.json({
      data: releases,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error("获取版本列表失败:", error)
    return NextResponse.json(
      { error: "获取版本列表失败" },
      { status: 500 }
    )
  }
}

// POST - 创建新版本
export async function POST(request: NextRequest) {
  try {
    const { version, description, createdBy, namespaceId, status = 'DRAFT' } = await request.json()

    if (!version || !createdBy || !namespaceId) {
      return NextResponse.json(
        { error: "版本号、创建者ID和命名空间ID是必填项" },
        { status: 400 }
      )
    }

    // 检查版本号是否已存在
    const existingRelease = await prisma.release.findUnique({
      where: { version }
    })

    if (existingRelease) {
      return NextResponse.json(
        { error: "版本号已存在" },
        { status: 409 }
      )
    }

    const release = await prisma.release.create({
      data: {
        version,
        description,
        status: status as any, // 使用传入的状态或默认DRAFT
        namespaceId,
        createdBy
      },
      include: {
        namespace: {
          select: { id: true, name: true, displayName: true }
        },
        creator: {
          select: { id: true, username: true }
        },
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    // 异步清理Go服务缓存（不阻塞响应）
    if (release.namespace) {
      smartCacheInvalidation(
        'version',
        release.namespace.name
      ).catch(error => {
        console.error('Cache invalidation failed after release creation:', error)
      })
    }

    return NextResponse.json(release)

  } catch (error) {
    console.error("创建版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// PUT - 更新版本（仅限草稿状态）
export async function PUT(request: NextRequest) {
  try {
    const { id, version, description } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: "版本ID是必填项" },
        { status: 400 }
      )
    }

    // 检查版本是否存在
    const existingRelease = await prisma.release.findUnique({
      where: { id }
    })

    if (!existingRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 只有草稿状态的版本才能修改
    if (existingRelease.status !== "DRAFT") {
      return NextResponse.json(
        { error: "只有草稿状态的版本才能修改" },
        { status: 400 }
      )
    }

    // 如果更新版本号，检查在同一环境下新版本号是否已存在
    if (version && version !== existingRelease.version) {
      const versionExists = await prisma.release.findUnique({
        where: { 
          environmentId_version: {
            environmentId: existingRelease.environmentId,
            version
          }
        }
      })

      if (versionExists) {
        return NextResponse.json(
          { error: "版本号已存在" },
          { status: 409 }
        )
      }
    }

    const updatedRelease = await prisma.release.update({
      where: { id },
      data: {
        ...(version && { version }),
        ...(description !== undefined && { description }),
      },
      include: {
        creator: {
          select: { id: true, username: true }
        },
        basedOn: {
          select: { id: true, version: true }
        },
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    return NextResponse.json(updatedRelease)

  } catch (error) {
    console.error("更新版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// DELETE - 删除版本（仅限草稿状态）
export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json()

    if (!id) {
      return NextResponse.json(
        { error: "版本ID是必填项" },
        { status: 400 }
      )
    }

    // 检查版本是否存在
    const existingRelease = await prisma.release.findUnique({
      where: { id },
      include: {
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    if (!existingRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 只有草稿状态的版本才能删除
    if (existingRelease.status !== "DRAFT") {
      return NextResponse.json(
        { error: "只有草稿状态的版本才能删除" },
        { status: 400 }
      )
    }

    // 如果版本包含翻译键，需要先删除翻译键
    if (existingRelease._count.translationKeys > 0) {
      await prisma.translationKey.deleteMany({
        where: { releaseId: id }
      })
    }

    await prisma.release.delete({
      where: { id }
    })

    return NextResponse.json({ 
      message: `版本 ${existingRelease.version} 删除成功` 
    })

  } catch (error) {
    console.error("删除版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
