import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { invalidateReleaseListCache } from "@/lib/cache-invalidation"

const prisma = new PrismaClient()

// 发布版本
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: releaseId } = await params

    // 检查版本是否存在
    const release = await prisma.release.findUnique({
      where: { id: releaseId },
      include: {
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    if (!release) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 检查版本状态
    if (release.status !== "DRAFT") {
      return NextResponse.json(
        { error: "只有草稿状态的版本才能发布" },
        { status: 400 }
      )
    }

    // 检查是否有翻译键
    if (release._count.translationKeys === 0) {
      return NextResponse.json(
        { error: "版本中没有翻译键，无法发布" },
        { status: 400 }
      )
    }

    // 发布版本
    const publishedRelease = await prisma.release.update({
      where: { id: releaseId },
      data: {
        status: "PUBLISHED",
        publishedAt: new Date(),
        changes: JSON.stringify([
          ...JSON.parse(release.changes || "[]"),
          {
            type: "PUBLISHED",
            timestamp: new Date().toISOString(),
            keysCount: release._count.translationKeys
          }
        ])
      },
      include: {
        namespace: {
          select: { id: true, name: true, displayName: true }
        },
        creator: {
          select: { id: true, username: true }
        },
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    // 异步清理Go服务缓存（版本发布只需要更新版本列表缓存）
    if (publishedRelease.namespace) {
      invalidateReleaseListCache(publishedRelease.namespace.name).catch(error => {
        console.error('Release list cache invalidation failed after version publish:', error)
      })
    }

    return NextResponse.json({
      ...publishedRelease,
      message: `版本 ${release.version} 发布成功，包含 ${release._count.translationKeys} 个翻译键`
    })

  } catch (error) {
    console.error("发布版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
