import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

interface TranslationDiff {
  key: string
  type: 'added' | 'removed' | 'modified' | 'unchanged'
  current?: any
  previous?: any
  changes?: {
    language: string
    oldValue: string | null
    newValue: string | null
  }[]
}

interface VersionDiffResponse {
  currentVersion: {
    id: string
    version: string
    status: string
  }
  previousVersion: {
    id: string
    version: string
    status: string
  } | null
  summary: {
    added: number
    removed: number
    modified: number
    unchanged: number
    total: number
  }
  changes: TranslationDiff[]
}

// 获取版本与上一版本的diff
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const compareWith = searchParams.get('compareWith') // 可选：指定对比的版本ID

    // 获取当前版本信息
    const currentRelease = await prisma.release.findUnique({
      where: { id },
      include: {
        namespace: { select: { id: true, name: true, displayName: true } },
        translationKeys: {
          select: {
            id: true,
            key: true,
            zh_CN: true,
            zh_TW: true,
            en_US: true,
            ja_JP: true,
            ko_KR: true,
            fr_FR: true,
            de_DE: true,
            es_ES: true,
            pt_PT: true,
            ru_RU: true,
            ar_SA: true,
            bn_BD: true,
            hi_IN: true,
            th_TH: true,
            vi_VN: true,
            id_ID: true,
            tr_TR: true,
            ur_PK: true,
            fa_IR: true
          }
        }
      }
    })

    if (!currentRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 获取对比版本（指定版本或上一个版本）
    let previousRelease
    if (compareWith) {
      previousRelease = await prisma.release.findUnique({
        where: { id: compareWith },
        include: {
          translationKeys: {
            select: {
              id: true,
              key: true,
              zh_CN: true,
              zh_TW: true,
              en_US: true,
              ja_JP: true,
              ko_KR: true,
              fr_FR: true,
              de_DE: true,
              es_ES: true,
              pt_PT: true,
              ru_RU: true,
              ar_SA: true,
              bn_BD: true,
              hi_IN: true,
              th_TH: true,
              vi_VN: true,
              id_ID: true,
              tr_TR: true,
              ur_PK: true,
              fa_IR: true
            }
          }
        }
      })
    } else {
      // 获取同namespace下的上一个已发布版本
      previousRelease = await prisma.release.findFirst({
        where: {
          namespaceId: currentRelease.namespaceId,
          status: 'PUBLISHED',
          createdAt: { lt: currentRelease.createdAt }
        },
        orderBy: { createdAt: 'desc' },
        include: {
          translationKeys: {
            select: {
              id: true,
              key: true,
              zh_CN: true,
              zh_TW: true,
              en_US: true,
              ja_JP: true,
              ko_KR: true,
              fr_FR: true,
              de_DE: true,
              es_ES: true,
              pt_PT: true,
              ru_RU: true,
              ar_SA: true,
              bn_BD: true,
              hi_IN: true,
              th_TH: true,
              vi_VN: true,
              id_ID: true,
              tr_TR: true,
              ur_PK: true,
              fa_IR: true
            }
          }
        }
      })
    }

    // 语言字段列表
    const languageFields = [
      'zh_CN', 'zh_TW', 'en_US', 'ja_JP', 'ko_KR', 'fr_FR', 'de_DE', 
      'es_ES', 'pt_PT', 'ru_RU', 'ar_SA', 'bn_BD', 'hi_IN', 'th_TH', 
      'vi_VN', 'id_ID', 'tr_TR', 'ur_PK', 'fa_IR'
    ]

    // 构建翻译键映射
    const currentKeys = new Map(currentRelease.translationKeys.map(k => [k.key, k]))
    const previousKeys = new Map(previousRelease?.translationKeys.map(k => [k.key, k]) || [])

    const changes: TranslationDiff[] = []
    const summary = { added: 0, removed: 0, modified: 0, unchanged: 0, total: 0 }

    // 检查当前版本的翻译键
    for (const [key, currentKey] of currentKeys) {
      const previousKey = previousKeys.get(key)
      
      if (!previousKey) {
        // 新增的翻译键
        changes.push({
          key,
          type: 'added',
          current: currentKey
        })
        summary.added++
      } else {
        // 检查是否有修改
        const keyChanges: { language: string; oldValue: string | null; newValue: string | null }[] = []
        
        for (const lang of languageFields) {
          const currentValue = (currentKey as any)[lang]
          const previousValue = (previousKey as any)[lang]
          
          if (currentValue !== previousValue) {
            keyChanges.push({
              language: lang,
              oldValue: previousValue,
              newValue: currentValue
            })
          }
        }

        if (keyChanges.length > 0) {
          changes.push({
            key,
            type: 'modified',
            current: currentKey,
            previous: previousKey,
            changes: keyChanges
          })
          summary.modified++
        } else {
          changes.push({
            key,
            type: 'unchanged',
            current: currentKey,
            previous: previousKey
          })
          summary.unchanged++
        }
      }
    }

    // 检查删除的翻译键
    for (const [key, previousKey] of previousKeys) {
      if (!currentKeys.has(key)) {
        changes.push({
          key,
          type: 'removed',
          previous: previousKey
        })
        summary.removed++
      }
    }

    summary.total = summary.added + summary.removed + summary.modified + summary.unchanged

    const response: VersionDiffResponse = {
      currentVersion: {
        id: currentRelease.id,
        version: currentRelease.version,
        status: currentRelease.status
      },
      previousVersion: previousRelease ? {
        id: previousRelease.id,
        version: previousRelease.version,
        status: previousRelease.status
      } : null,
      summary,
      changes: changes.sort((a, b) => {
        // 排序：新增 -> 修改 -> 删除 -> 未变化
        const order = { added: 1, modified: 2, removed: 3, unchanged: 4 }
        return order[a.type] - order[b.type] || a.key.localeCompare(b.key)
      })
    }

    return NextResponse.json({
      message: "版本对比获取成功",
      data: response
    })

  } catch (error) {
    console.error("获取版本对比错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
