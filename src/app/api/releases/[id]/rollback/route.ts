import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// 回滚到指定版本
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: targetReleaseId } = await params
    const body = await request.json()
    const { environmentId, deployedBy } = body

    if (!environmentId || !deployedBy) {
      return NextResponse.json(
        { error: "环境ID和部署者ID是必填项" },
        { status: 400 }
      )
    }

    // 检查目标版本是否存在且已发布
    const targetRelease = await prisma.release.findUnique({
      where: { id: targetReleaseId }
    })

    if (!targetRelease) {
      return NextResponse.json(
        { error: "目标版本不存在" },
        { status: 404 }
      )
    }

    if (targetRelease.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "只能回滚到已发布的版本" },
        { status: 400 }
      )
    }

    // 检查环境是否存在
    const environment = await prisma.environment.findUnique({
      where: { id: environmentId }
    })

    if (!environment) {
      return NextResponse.json(
        { error: "环境不存在" },
        { status: 404 }
      )
    }

    // 检查该环境当前部署的版本
    const currentDeployment = await prisma.deployment.findFirst({
      where: {
        environmentId,
        status: "SUCCESS"
      },
      orderBy: {
        deployedAt: 'desc'
      },
      include: {
        release: true
      }
    })

    if (currentDeployment && currentDeployment.releaseId === targetReleaseId) {
      return NextResponse.json(
        { error: "该版本已经是当前环境的活跃版本" },
        { status: 400 }
      )
    }

    // 使用事务进行回滚操作
    const result = await prisma.$transaction(async (tx) => {
      // 1. 将当前环境的活跃部署标记为回滚状态
      if (currentDeployment) {
        await tx.deployment.update({
          where: { id: currentDeployment.id },
          data: {
            status: "ROLLBACK",
            rollbackAt: new Date()
          }
        })
      }

      // 2. 创建新的部署记录（回滚部署）
      const newDeployment = await tx.deployment.create({
        data: {
          releaseId: targetReleaseId,
          environmentId,
          deployedBy,
          status: "SUCCESS",
          notes: `回滚到版本 ${targetRelease.version}`
        },
        include: {
          release: {
            include: {
              creator: {
                select: { id: true, username: true }
              },
              _count: {
                select: { translationKeys: true }
              }
            }
          },
          environment: true,
          deployer: {
            select: { id: true, username: true }
          }
        }
      })

      return newDeployment
    })

    return NextResponse.json({
      ...result,
      message: `成功回滚到版本 ${targetRelease.version} 在环境 ${environment.name}`
    })

  } catch (error) {
    console.error("回滚版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
