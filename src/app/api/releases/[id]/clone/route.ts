import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { prisma } from "@/lib/prisma"
import { Prisma } from "@prisma/client"

// 基于已有版本创建新版本
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    let version, description, createdBy
    
    // 尝试解析JSON，如果没有body则使用默认值
    try {
      const body = await request.json()
      version = body.version
      description = body.description
      createdBy = body.createdBy
    } catch (e) {
      // 如果没有JSON body，使用默认值进行自动克隆
      version = null // 将在后面自动生成
      description = null // 将在后面自动生成
      // 如果没有提供用户ID，从会话获取
      const session = await getServerSession(authOptions)
      if (session?.user?.name) {
        const user = await prisma.user.findUnique({
          where: { username: session.user.name },
          select: { id: true }
        })
        createdBy = user?.id || "cme29c55n0000zuy9yqi7iqnn" // 备用默认ID
      } else {
        createdBy = "cme29c55n0000zuy9yqi7iqnn" // 备用默认ID
      }
    }

    const { id: sourceReleaseId } = await params

    // 检查源版本是否存在
    const sourceRelease = await prisma.release.findUnique({
      where: { id: sourceReleaseId },
      include: {
        translationKeys: true
      }
    })

    if (!sourceRelease) {
      return NextResponse.json(
        { error: "源版本不存在" },
        { status: 404 }
      )
    }

    // 如果没有提供版本号，自动生成一个
    if (!version) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '-')
      version = `${sourceRelease.version}-clone-${timestamp}`
    }
    
    if (!description) {
      description = `基于版本 ${sourceRelease.version} 创建`
    }

    // 检查新版本号是否已存在
    const existingRelease = await prisma.release.findUnique({
      where: { version }
    })

    if (existingRelease) {
      return NextResponse.json(
        { error: "版本号已存在" },
        { status: 409 }
      )
    }

    // 使用事务创建新版本和复制翻译键
    const result = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // 1. 创建新版本
      const newRelease = await tx.release.create({
        data: {
          version,
          description,
          status: "DRAFT",
          namespaceId: sourceRelease.namespaceId,
          basedOnId: sourceReleaseId,
          createdBy,
          changes: JSON.stringify([
            {
              type: "CLONED_FROM",
              sourceVersion: sourceRelease.version,
              timestamp: new Date().toISOString(),
              keysCount: sourceRelease.translationKeys.length
            }
          ])
        }
      })

      // 2. 复制所有翻译键（新版本中可以有相同的key）
      let copiedCount = 0
      
      if (sourceRelease.translationKeys.length > 0) {
        for (const key of sourceRelease.translationKeys) {
          // 直接复制翻译键到新版本，因为新的唯一约束允许不同版本有相同key
          try {
            await tx.translationKey.create({
              data: {
                key: key.key,
                namespaceId: key.namespaceId,
                description: key.description,
                createdBy: createdBy,
                releaseId: newRelease.id,
                zh_CN: key.zh_CN,
                zh_TW: key.zh_TW,
                en_US: key.en_US,
                ja_JP: key.ja_JP,
                ko_KR: key.ko_KR,
                fr_FR: key.fr_FR,
                de_DE: key.de_DE,
                es_ES: key.es_ES,
                pt_PT: key.pt_PT,
                ru_RU: key.ru_RU,
                ar_SA: key.ar_SA,
                bn_BD: key.bn_BD,
                hi_IN: key.hi_IN,
                th_TH: key.th_TH,
                vi_VN: key.vi_VN,
                id_ID: key.id_ID,
                tr_TR: key.tr_TR,
                ur_PK: key.ur_PK,
                fa_IR: key.fa_IR
              }
            })
            copiedCount++
          } catch (error) {
            console.error(`复制翻译键 ${key.key} 失败:`, error)
            // 继续处理其他键，不中断整个过程
          }
        }
      }
      
      // 更新变更记录
      await tx.release.update({
        where: { id: newRelease.id },
        data: {
          changes: JSON.stringify([
            {
              type: "CLONED_FROM",
              sourceVersion: sourceRelease.version,
              timestamp: new Date().toISOString(),
              totalKeys: sourceRelease.translationKeys.length,
              copiedKeys: copiedCount
            }
          ])
        }
      })

      return { newRelease, copiedCount }
    })

    // 获取完整的新版本信息（包含翻译键数量）
    const newReleaseWithCount = await prisma.release.findUnique({
      where: { id: result.newRelease.id },
      include: {
        creator: {
          select: { id: true, username: true }
        },
        basedOn: {
          select: { id: true, version: true }
        },
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    // 构建详细的结果消息
    const message = `成功基于版本 ${sourceRelease.version} 创建新版本 ${version}，复制了 ${result.copiedCount} 个翻译键`

    return NextResponse.json({
      ...newReleaseWithCount,
      message,
      cloneStats: {
        totalKeys: sourceRelease.translationKeys.length,
        copiedKeys: result.copiedCount
      }
    })

  } catch (error) {
    console.error("克隆版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
