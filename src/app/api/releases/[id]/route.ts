import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// PUT - 更新版本（仅限草稿状态）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { version, description, namespaceId } = body

    if (!version) {
      return NextResponse.json(
        { error: "版本号是必填项" },
        { status: 400 }
      )
    }

    // 检查版本是否存在
    const existingRelease = await prisma.release.findUnique({
      where: { id }
    })

    if (!existingRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 只有草稿状态的版本才能编辑
    if (existingRelease.status !== "DRAFT") {
      return NextResponse.json(
        { error: "只有草稿状态的版本才能编辑" },
        { status: 400 }
      )
    }

    // 检查版本号是否已存在（排除当前版本）
    if (version !== existingRelease.version) {
      const duplicateRelease = await prisma.release.findFirst({
        where: {
          version,
          namespaceId: namespaceId || existingRelease.namespaceId,
          id: { not: id }
        }
      })

      if (duplicateRelease) {
        return NextResponse.json(
          { error: "该命名空间中已存在相同版本号的版本" },
          { status: 409 }
        )
      }
    }

    // 更新版本
    const updatedRelease = await prisma.release.update({
      where: { id },
      data: {
        version,
        description,
        namespaceId: namespaceId || existingRelease.namespaceId,
        updatedAt: new Date()
      },
      include: {
        namespace: {
          select: { id: true, name: true, displayName: true }
        },
        creator: {
          select: { id: true, username: true }
        },
        basedOn: {
          select: { id: true, version: true }
        },
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    return NextResponse.json(updatedRelease)

  } catch (error) {
    console.error("更新版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}

// DELETE - 删除版本（仅限草稿状态）
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查版本是否存在
    const existingRelease = await prisma.release.findUnique({
      where: { id },
      include: {
        _count: {
          select: { translationKeys: true }
        }
      }
    })

    if (!existingRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 只有草稿状态的版本才能删除
    if (existingRelease.status !== "DRAFT") {
      return NextResponse.json(
        { error: "只有草稿状态的版本才能删除" },
        { status: 400 }
      )
    }

    // 检查是否有翻译键关联
    if (existingRelease._count.translationKeys > 0) {
      return NextResponse.json(
        { error: "该版本包含翻译键，无法删除。请先删除所有相关的翻译键。" },
        { status: 400 }
      )
    }

    // 删除版本
    await prisma.release.delete({
      where: { id }
    })

    return NextResponse.json({ message: "版本删除成功" })

  } catch (error) {
    console.error("删除版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
