import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: releaseId } = await params
    const body = await request.json()
    const { environmentId, deployedBy, notes } = body

    if (!environmentId || !deployedBy) {
      return NextResponse.json(
        { error: "环境ID和部署者ID是必填项" },
        { status: 400 }
      )
    }

    // 检查目标版本是否存在且已发布
    const targetRelease = await prisma.release.findUnique({
      where: { id: releaseId },
      select: { id: true, version: true, status: true }
    })

    if (!targetRelease) {
      return NextResponse.json(
        { error: "目标版本不存在" },
        { status: 404 }
      )
    }

    if (targetRelease.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "只能部署已发布的版本" },
        { status: 400 }
      )
    }

    // 检查环境是否存在
    const environment = await prisma.environment.findUnique({
      where: { id: environmentId },
      select: { id: true, name: true }
    })

    if (!environment) {
      return NextResponse.json(
        { error: "目标环境不存在" },
        { status: 404 }
      )
    }

    // 检查该环境是否已经部署了这个版本
    const existingDeployment = await prisma.deployment.findUnique({
      where: {
        environmentId_releaseId: {
          environmentId,
          releaseId
        }
      }
    })

    if (existingDeployment && existingDeployment.status === "SUCCESS") {
      return NextResponse.json(
        { error: `版本 ${targetRelease.version} 已经在 ${environment.name} 环境中部署` },
        { status: 400 }
      )
    }

    // 使用事务进行部署操作
    const result = await prisma.$transaction(async (tx) => {
      // 1. 将该环境的当前成功部署标记为历史记录（如果存在）
      await tx.deployment.updateMany({
        where: {
          environmentId,
          status: "SUCCESS"
        },
        data: {
          status: "ROLLBACK",
          rollbackAt: new Date()
        }
      })

      // 2. 创建新的部署记录
      const newDeployment = await tx.deployment.create({
        data: {
          releaseId,
          environmentId,
          deployedBy,
          status: "SUCCESS",
          notes: notes || `部署版本 ${targetRelease.version} 到 ${environment.name} 环境`
        },
        include: {
          release: {
            select: { version: true }
          },
          environment: {
            select: { name: true }
          }
        }
      })

      return newDeployment
    })

    return NextResponse.json({
      message: `版本 ${targetRelease.version} 成功部署到 ${environment.name} 环境`,
      deployment: result
    })

  } catch (error) {
    console.error("部署版本错误:", error)
    return NextResponse.json(
      { error: "部署失败，请重试" },
      { status: 500 }
    )
  }
}
