import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// POST - 归档版本
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 检查版本是否存在
    const existingRelease = await prisma.release.findUnique({
      where: { id }
    })

    if (!existingRelease) {
      return NextResponse.json(
        { error: "版本不存在" },
        { status: 404 }
      )
    }

    // 只有已发布的版本才能归档
    if (existingRelease.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "只有已发布的版本才能归档" },
        { status: 400 }
      )
    }

    // 更新版本状态为归档
    const archivedRelease = await prisma.release.update({
      where: { id },
      data: {
        status: "ARCHIVED",
        updatedAt: new Date()
      },
      select: {
        id: true,
        version: true,
        description: true,
        status: true,
        namespaceId: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return NextResponse.json(archivedRelease)

  } catch (error) {
    console.error("归档版本错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
