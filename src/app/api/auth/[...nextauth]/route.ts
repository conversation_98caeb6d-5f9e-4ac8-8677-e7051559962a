import NextAuth, { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { prisma } from "@/lib/db"
import { Client } from "ldapts"

// LDAP认证函数
async function authenticateLDAP(username: string, password: string): Promise<any> {
  const client = new Client({
    url: process.env.LDAP_URL || 'ldap://localhost:389',
    timeout: 5000,
    connectTimeout: 5000,
  })

  try {
    // 第一步：使用查询账号绑定LDAP服务器
    const bindDN = process.env.LDAP_BIND_DN
    const bindPassword = process.env.LDAP_BIND_PASSWORD
    
    if (!bindDN || !bindPassword) {
      throw new Error('LDAP_BIND_DN and LDAP_BIND_PASSWORD must be configured')
    }

    console.log('LDAP Debug Info:', {
      username,
      ldapUrl: process.env.LDAP_URL,
      baseDN: process.env.LDAP_BASE_DN,
      bindDN: bindDN,
      searchFilter: process.env.LDAP_SEARCH_FILTER?.replace('{username}', username)
    })

    // 使用查询账号绑定
    await client.bind(bindDN, bindPassword)
    console.log('LDAP bind successful with query account:', bindDN)

    // 第二步：搜索用户信息
    const searchBase = process.env.LDAP_BASE_DN || 'dc=xme,dc=world'
    const searchFilter = process.env.LDAP_SEARCH_FILTER?.replace('{username}', username) || `(cn=${username})`

    const { searchEntries } = await client.search(searchBase, {
      scope: 'sub',
      filter: searchFilter,
      attributes: ['cn', 'mail', 'displayName', 'memberOf', 'sAMAccountName', 'uid', 'dn']
    })

    if (searchEntries.length === 0) {
      console.log('User not found in LDAP:', username)
      return null
    }

    const user = searchEntries[0]
    const userDN = user.dn
    console.log('Found user DN:', userDN)

    // 第三步：使用找到的用户DN验证密码
    const userClient = new Client({
      url: process.env.LDAP_URL || 'ldap://localhost:389',
      timeout: 5000,
      connectTimeout: 5000,
    })

    try {
      await userClient.bind(userDN, password)
      console.log('User password verification successful for:', userDN)
      
      return {
        id: user.sAMAccountName || user.uid || user.cn || username,
        username: username,
        email: user.mail || `${username}@xme.world`,
        name: user.displayName || user.cn || username,
        groups: Array.isArray(user.memberOf) ? user.memberOf : (user.memberOf ? [user.memberOf] : [])
      }
    } catch (userAuthError) {
      console.log('User password verification failed:', userAuthError.message)
      return null
    } finally {
      try {
        await userClient.unbind()
      } catch (error) {
        // 忽略解绑错误
      }
    }

  } catch (error) {
    console.error('LDAP authentication error:', error)
    return null
  } finally {
    try {
      await client.unbind()
    } catch (error) {
      // 忽略解绑错误
    }
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "ldap",
      name: "LDAP",
      credentials: {
        username: { label: "用户名", type: "text" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null
        }

        try {
          // LDAP认证
          const ldapUser = await authenticateLDAP(credentials.username, credentials.password)
          
          if (!ldapUser) {
            return null
          }

          // 检查用户是否在允许的组中（可选）
          const allowedGroups = process.env.LDAP_ALLOWED_GROUPS?.split(',') || []
          if (allowedGroups.length > 0) {
            const hasAllowedGroup = ldapUser.groups.some((group: string) => 
              allowedGroups.some(allowedGroup => group.includes(allowedGroup))
            )
            if (!hasAllowedGroup) {
              throw new Error("AccessDenied")
            }
          }

          // 同步用户信息到数据库（可选）
          try {
            await prisma.user.upsert({
              where: { username: ldapUser.username },
              update: {
                email: ldapUser.email,
                isActive: true
              },
              create: {
                username: ldapUser.username,
                email: ldapUser.email,
                hashedPassword: '', // LDAP用户不需要密码
                isActive: true
              }
            })
          } catch (dbError) {
            console.warn('Failed to sync user to database:', dbError)
            // 即使数据库同步失败，也允许登录
          }

          return {
            id: ldapUser.id,
            email: ldapUser.email,
            username: ldapUser.username,
            name: ldapUser.name
          }
        } catch (error) {
          console.error('LDAP authentication error:', error)
          if (error instanceof Error && error.message === "AccessDenied") {
            throw error
          }
          return null
        }
      }
    })
  ],
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.username = user.username
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.username = token.username as string
        session.user.name = token.username as string // 确保 name 和 username 一致
      }
      return session
    },
  },
}

const handler = NextAuth(authOptions)

export { handler as GET, handler as POST } 