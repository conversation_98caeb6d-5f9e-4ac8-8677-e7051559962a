import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"
import { prisma } from "@/lib/prisma"

// GET - 获取当前用户信息
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.username) {
      return NextResponse.json(
        { error: "未登录" },
        { status: 401 }
      )
    }

    // 根据用户名查找用户
    const user = await prisma.user.findUnique({
      where: { username: session.user.username },
      select: {
        id: true,
        username: true,
        email: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "用户未找到" },
        { status: 404 }
      )
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error("获取用户信息错误:", error)
    return NextResponse.json(
      { error: "服务器内部错误" },
      { status: 500 }
    )
  }
}
