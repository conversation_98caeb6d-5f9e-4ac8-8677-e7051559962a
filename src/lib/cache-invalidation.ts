/**
 * Go服务缓存失效工具函数
 * 用于在前端数据修改后自动清理Go后台缓存
 */

// 支持多个Go服务实例
const GO_SERVICE_URLS = process.env.NEXT_PUBLIC_GO_SERVICE_URLS 
  ? process.env.NEXT_PUBLIC_GO_SERVICE_URLS.split(',')
  : [process.env.NEXT_PUBLIC_GO_SERVICE_URL || 'http://localhost:8080'];

export interface CacheInvalidationResponse {
  success: boolean;
  message: string;
}

/**
 * 向单个Go服务实例发送缓存失效请求
 */
async function invalidateCacheOnInstance(url: string, endpoint: string): Promise<CacheInvalidationResponse> {
  try {
    const response = await fetch(`${url}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`❌ Failed to invalidate cache on ${url}:`, error);
    return { success: false, message: `Failed to invalidate cache on ${url}` };
  }
}

/**
 * 按命名空间和版本精确失效缓存（支持多实例）
 * 这是主要的缓存失效方法，用于前端数据变更后清理相关缓存
 */
export async function invalidateNamespaceVersionCache(
  namespace: string, 
  version?: string
): Promise<CacheInvalidationResponse> {
  const endpoint = version 
    ? `/i18n/api/v1/cache/namespace/${namespace}/clear?version=${version}`
    : `/i18n/api/v1/cache/namespace/${namespace}/clear`;
  
  // 并行向所有Go服务实例发送请求
  const promises = GO_SERVICE_URLS.map(url => invalidateCacheOnInstance(url, endpoint));
  const results = await Promise.all(promises);
  
  // 统计成功和失败的实例
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  const versionInfo = version ? ` (version: ${version})` : '';
  if (successCount === totalCount) {
    console.log(`✅ Cache for namespace '${namespace}'${versionInfo} invalidated successfully on ${totalCount} instances`);
    return { success: true, message: `Cache invalidated on all ${totalCount} instances` };
  } else {
    console.warn(`⚠️ Cache for namespace '${namespace}'${versionInfo} invalidated on ${successCount}/${totalCount} instances`);
    return { 
      success: successCount > 0, 
      message: `Cache invalidated on ${successCount}/${totalCount} instances` 
    };
  }
}

// 注意：已移除 invalidateTranslationCache 和 invalidateVersionCache 函数
// 现在统一使用 invalidateNamespaceVersionCache 进行精确缓存失效

/**
 * 清空版本列表缓存（用于版本发布等状态变更）
 * 版本发布时只需要更新版本列表，不需要清空翻译缓存
 */
export async function invalidateReleaseListCache(namespace: string): Promise<CacheInvalidationResponse> {
  const endpoint = `/i18n/api/v1/cache/releases/${namespace}/clear`;
  
  // 并行向所有Go服务实例发送请求
  const promises = GO_SERVICE_URLS.map(url => invalidateCacheOnInstance(url, endpoint));
  const results = await Promise.all(promises);
  
  // 统计成功和失败的实例
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  if (successCount === totalCount) {
    console.log(`✅ Release list cache for namespace '${namespace}' invalidated successfully on ${totalCount} instances`);
    return { success: true, message: `Release list cache invalidated on all ${totalCount} instances` };
  } else {
    console.warn(`⚠️ Release list cache for namespace '${namespace}' invalidated on ${successCount}/${totalCount} instances`);
    return { 
      success: successCount > 0, 
      message: `Release list cache invalidated on ${successCount}/${totalCount} instances` 
    };
  }
}

/**
 * 智能缓存失效 - 根据操作类型自动选择失效策略
 * 支持两种调用格式以保持兼容性
 */
export async function smartCacheInvalidation(
  operationOrOptions: 'translation' | 'version' | 'namespace' | {
    operation: 'translation' | 'version' | 'namespace';
    namespace?: string;
  },
  namespace?: string,
  version?: string
): Promise<void> {
  let operation: string;
  let targetNamespace: string;
  let targetVersion: string | undefined;

  // 支持旧的对象格式调用
  if (typeof operationOrOptions === 'object') {
    operation = operationOrOptions.operation;
    targetNamespace = operationOrOptions.namespace || '';
    targetVersion = version;
  } else {
    // 新的分离参数格式
    operation = operationOrOptions;
    targetNamespace = namespace || '';
    targetVersion = version;
  }

  console.log(`🔄 Smart cache invalidation: ${operation}`, { namespace: targetNamespace, version: targetVersion });
  
  if (!targetNamespace) {
    console.warn('Namespace is required for cache invalidation');
    return;
  }
  
  switch (operation) {
    case 'translation':
      // 翻译变更：清空指定命名空间和版本的缓存
      await invalidateNamespaceVersionCache(targetNamespace, targetVersion);
      break;
    
    case 'version':
      // 版本变更：清空指定命名空间的缓存（所有版本）
      await invalidateNamespaceVersionCache(targetNamespace);
      break;
    
    case 'namespace':
      // 命名空间变更：清空该命名空间的所有缓存
      await invalidateNamespaceVersionCache(targetNamespace);
      break;
    
    default:
      console.warn('Unknown cache invalidation operation:', operation);
  }
}

/**
 * 批量缓存失效 - 用于批量操作后的缓存清理
 */
export async function batchCacheInvalidation(namespaces: string[]): Promise<void> {
  // 逐个清理命名空间缓存
  const promises = namespaces.map(namespace => 
    invalidateNamespaceVersionCache(namespace)
  );
  
  await Promise.all(promises);
}
