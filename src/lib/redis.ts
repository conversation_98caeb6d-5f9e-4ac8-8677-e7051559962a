import { createClient, createCluster } from 'redis'

// Redis配置
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'

// 判断是否为集群配置
const isCluster = REDIS_URL.includes('clustercfg') || process.env.REDIS_CLUSTER === 'true'

let redisClient: any = null

export async function getRedisClient() {
  if (redisClient) {
    return redisClient
  }

  try {
    if (isCluster) {
      // Redis集群配置
      const clusterNodes = process.env.REDIS_CLUSTER_NODES?.split(',') || [REDIS_URL]
      
      redisClient = createCluster({
        rootNodes: clusterNodes.map(node => ({ url: node })),
        defaults: {
          socket: {
            timeout: 5000,
            connectTimeout: 5000,
          }
        }
      })
      
      console.log('Connecting to Redis Cluster:', clusterNodes)
    } else {
      // 单机Redis配置
      redisClient = createClient({
        url: REDIS_URL,
        socket: {
          timeout: 5000,
          connectTimeout: 5000,
        }
      })
      
      console.log('Connecting to Redis:', REDIS_URL)
    }

    redisClient.on('error', (err: Error) => {
      console.error('Redis Client Error:', err)
    })

    redisClient.on('connect', () => {
      console.log('Redis Client Connected')
    })

    await redisClient.connect()
    return redisClient
  } catch (error) {
    console.error('Failed to connect to Redis:', error)
    throw error
  }
}

export async function closeRedisClient() {
  if (redisClient) {
    await redisClient.quit()
    redisClient = null
  }
}

// 导出Redis客户端实例
export { redisClient }
