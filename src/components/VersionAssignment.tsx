"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "sonner"
import { Package, GitBranch, CheckSquare, Square } from "lucide-react"

interface Namespace {
  id: string
  name: string
  displayName: string
}

interface Release {
  id: string
  version: string
  status: string
  namespace: Namespace
}

interface TranslationKey {
  id: string
  key: string
  namespaceId: string
  releaseId: string | null
  namespace: Namespace
  release?: Release
  zh_CN?: string
  en_US?: string
}

export default function VersionAssignment() {
  const [translationKeys, setTranslationKeys] = useState<TranslationKey[]>([])
  const [releases, setReleases] = useState<Release[]>([])
  const [namespaces, setNamespaces] = useState<Namespace[]>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const [selectedNamespace, setSelectedNamespace] = useState("")
  const [targetRelease, setTargetRelease] = useState("")
  const [loading, setLoading] = useState(false)
  const [showAssignDialog, setShowAssignDialog] = useState(false)

  useEffect(() => {
    fetchNamespaces()
  }, [])

  useEffect(() => {
    if (selectedNamespace) {
      fetchUnassignedKeys()
      fetchReleases()
    }
  }, [selectedNamespace])

  const fetchNamespaces = async () => {
    try {
      const response = await fetch("/api/namespaces")
      const data = await response.json()
      setNamespaces(data.data || [])
    } catch (error) {
      toast.error("获取命名空间失败")
    }
  }

  const fetchUnassignedKeys = async () => {
    if (!selectedNamespace) return
    
    try {
      setLoading(true)
      const response = await fetch(`/api/translation-keys?namespaceId=${selectedNamespace}&releaseId=unassigned`)
      const data = await response.json()
      setTranslationKeys(data.data || [])
    } catch (error) {
      toast.error("获取未分配翻译键失败")
    } finally {
      setLoading(false)
    }
  }

  const fetchReleases = async () => {
    if (!selectedNamespace) return
    
    try {
      const response = await fetch(`/api/releases?namespaceId=${selectedNamespace}&status=DRAFT`)
      const data = await response.json()
      setReleases(data.data || [])
    } catch (error) {
      toast.error("获取版本列表失败")
    }
  }

  const handleBatchAssign = async () => {
    if (!targetRelease || selectedKeys.length === 0) {
      toast.error("请选择目标版本和翻译键")
      return
    }

    try {
      setLoading(true)
      const promises = selectedKeys.map(keyId => 
        fetch(`/api/translation-keys/${keyId}`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ releaseId: targetRelease }),
        })
      )

      const results = await Promise.all(promises)
      const successCount = results.filter(r => r.ok).length

      if (successCount === selectedKeys.length) {
        toast.success(`成功分配 ${successCount} 个翻译键到版本`)
      } else {
        toast.warning(`分配了 ${successCount}/${selectedKeys.length} 个翻译键`)
      }

      setShowAssignDialog(false)
      setSelectedKeys([])
      setTargetRelease("")
      fetchUnassignedKeys()
    } catch (error) {
      toast.error("批量分配失败")
    } finally {
      setLoading(false)
    }
  }

  const handleSingleAssign = async (keyId: string, releaseId: string) => {
    try {
      const response = await fetch(`/api/translation-keys/${keyId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ releaseId }),
      })

      if (response.ok) {
        toast.success("版本分配成功")
        fetchUnassignedKeys()
      } else {
        toast.error("版本分配失败")
      }
    } catch (error) {
      toast.error("版本分配失败")
    }
  }

  const toggleKeySelection = (keyId: string) => {
    setSelectedKeys(prev => 
      prev.includes(keyId) 
        ? prev.filter(id => id !== keyId)
        : [...prev, keyId]
    )
  }

  const toggleSelectAll = () => {
    if (selectedKeys.length === translationKeys.length) {
      setSelectedKeys([])
    } else {
      setSelectedKeys(translationKeys.map(key => key.id))
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            版本分配管理
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 命名空间选择 */}
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label className="text-sm font-medium">选择命名空间</label>
                <Select value={selectedNamespace} onValueChange={setSelectedNamespace}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择命名空间" />
                  </SelectTrigger>
                  <SelectContent>
                    {namespaces.map((ns) => (
                      <SelectItem key={ns.id} value={ns.id}>
                        {ns.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {selectedNamespace && (
                <Button onClick={fetchUnassignedKeys} disabled={loading}>
                  刷新数据
                </Button>
              )}
            </div>

            {/* 批量操作 */}
            {selectedNamespace && translationKeys.length > 0 && (
              <div className="flex gap-2 items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleSelectAll}
                  className="flex items-center gap-2"
                >
                  {selectedKeys.length === translationKeys.length ? (
                    <CheckSquare className="h-4 w-4" />
                  ) : (
                    <Square className="h-4 w-4" />
                  )}
                  {selectedKeys.length === translationKeys.length ? "取消全选" : "全选"}
                </Button>
                
                {selectedKeys.length > 0 && (
                  <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        批量分配版本 ({selectedKeys.length})
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>批量分配版本</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">目标版本</label>
                          <Select value={targetRelease} onValueChange={setTargetRelease}>
                            <SelectTrigger>
                              <SelectValue placeholder="选择版本" />
                            </SelectTrigger>
                            <SelectContent>
                              {releases.filter(r => r.status === "DRAFT").map((release) => (
                                <SelectItem key={release.id} value={release.id}>
                                  {release.version} (草稿)
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex gap-2">
                          <Button onClick={handleBatchAssign} disabled={loading || !targetRelease}>
                            确认分配
                          </Button>
                          <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
                            取消
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 未分配翻译键列表 */}
      {selectedNamespace && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              未分配版本的翻译键 ({translationKeys.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">加载中...</div>
            ) : translationKeys.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                该命名空间下没有未分配版本的翻译键
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">选择</TableHead>
                    <TableHead>翻译键</TableHead>
                    <TableHead>中文</TableHead>
                    <TableHead>英文</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {translationKeys.map((key) => (
                    <TableRow key={key.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedKeys.includes(key.id)}
                          onCheckedChange={() => toggleKeySelection(key.id)}
                        />
                      </TableCell>
                      <TableCell className="font-mono text-sm">{key.key}</TableCell>
                      <TableCell>{key.zh_CN || "-"}</TableCell>
                      <TableCell>{key.en_US || "-"}</TableCell>
                      <TableCell>
                        <Select onValueChange={(value) => handleSingleAssign(key.id, value)}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="分配到版本" />
                          </SelectTrigger>
                          <SelectContent>
                            {releases.filter(r => r.status === "DRAFT").map((release) => (
                              <SelectItem key={release.id} value={release.id}>
                                {release.version}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
