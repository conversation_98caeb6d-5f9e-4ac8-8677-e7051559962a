"use client"

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { Plus, Package, Trash2 } from "lucide-react"

interface Namespace {
  id: string
  name: string
  displayName: string
  description?: string
  createdBy: string
  _count: {
    keys: number
  }
  createdAt: string
  updatedAt: string
}

export default function NamespaceManagement() {
  const { data: session } = useSession()
  const [namespaces, setNamespaces] = useState<Namespace[]>([])
  const [loading, setLoading] = useState(true)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const { success, error } = useToast()

  const [newNamespace, setNewNamespace] = useState({
    name: '',
    displayName: '',
    description: '',
    createdBy: ''
  })

  // 获取当前用户ID
  useEffect(() => {
    const fetchCurrentUser = async () => {
      if (session?.user?.name) {
        try {
          const response = await fetch('/api/auth/me')
          if (response.ok) {
            const userData = await response.json()
            setCurrentUserId(userData.id)
            setNewNamespace(prev => ({ ...prev, createdBy: userData.id }))
          }
        } catch (error) {
          console.error('获取用户信息失败:', error)
        }
      }
    }

    fetchCurrentUser()
  }, [session])

  useEffect(() => {
    fetchNamespaces()
  }, [])

  const fetchNamespaces = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/namespaces')
      const result = await response.json()
      
      // 支持新的分页格式
      if (result.data && Array.isArray(result.data)) {
        setNamespaces(result.data)
      } else if (Array.isArray(result)) {
        // 兼容旧格式
        setNamespaces(result)
      } else {
        setNamespaces([])
      }
    } catch (err) {
      error("获取命名空间列表失败")
    } finally {
      setLoading(false)
    }
  }

  const handleCreateNamespace = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!newNamespace.name || !newNamespace.displayName) {
      error("请填写所有必填字段")
      return
    }

    try {
      const response = await fetch('/api/namespaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newNamespace),
      })

      if (response.ok) {
        success("命名空间创建成功")
        setCreateDialogOpen(false)
        setNewNamespace({ name: '', displayName: '', description: '', createdBy: currentUserId || '' })
        fetchNamespaces()
      } else {
        const errorData = await response.json()
        error(errorData.error || "创建命名空间失败")
      }
    } catch (err) {
      error("创建命名空间失败")
    }
  }

  const handleDeleteNamespace = async (id: string) => {
    if (!confirm("确定要删除这个命名空间吗？")) return

    try {
      const response = await fetch(`/api/namespaces/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        success("命名空间删除成功")
        fetchNamespaces()
      } else {
        const errorData = await response.json()
        error(errorData.error || "删除命名空间失败")
      }
    } catch (err) {
      error("删除命名空间失败")
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">命名空间管理</h2>
          <p className="text-muted-foreground">
            管理应用的命名空间，组织和隔离不同的模块
          </p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              创建命名空间
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>创建新命名空间</DialogTitle>
              <DialogDescription>
                为应用创建一个新的命名空间来组织模块
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateNamespace} className="space-y-4">

              <div className="space-y-2">
                <Label htmlFor="namespace-name">命名空间名称 *</Label>
                <Input
                  id="namespace-name"
                  value={newNamespace.name}
                  onChange={(e) => setNewNamespace({ ...newNamespace, name: e.target.value })}
                  placeholder="例如：user-service"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="namespace-display-name">显示名称 *</Label>
                <Input
                  id="namespace-display-name"
                  value={newNamespace.displayName}
                  onChange={(e) => setNewNamespace({ ...newNamespace, displayName: e.target.value })}
                  placeholder="例如：用户服务"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="namespace-description">描述</Label>
                <Textarea
                  id="namespace-description"
                  value={newNamespace.description}
                  onChange={(e) => setNewNamespace({ ...newNamespace, description: e.target.value })}
                  placeholder="描述命名空间的用途..."
                  rows={3}
                />
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button type="submit">
                  创建命名空间
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>



      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>命名空间名称</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>翻译键数量</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  加载中...
                </TableCell>
              </TableRow>
            ) : namespaces.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                  暂无命名空间数据
                </TableCell>
              </TableRow>
            ) : (
              namespaces.map((namespace) => (
                <TableRow key={namespace.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-medium">{namespace.displayName}</div>
                      <div className="text-sm text-gray-500">{namespace.name}</div>
                    </div>
                  </TableCell>
                  <TableCell>{namespace.description || "无描述"}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      <Package className="h-3 w-3 mr-1" />
                      {namespace._count.keys} 个翻译键
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(namespace.createdAt).toLocaleDateString('zh-CN')}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteNamespace(namespace.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}


