"use client"

import { useState, useEffect } from "react"
import { useCurrentUser } from "@/hooks/use-current-user"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { Globe, Edit, Wand2, Clipboard } from "lucide-react"

interface TranslationKey {
  id: string
  key: string
  description?: string
  namespace: {
    id: string
    name: string
    displayName: string
  }
  release?: {
    id: string
    version: string
    status: string
  }
  [key: string]: any // 用于语言字段
}

interface Namespace {
  id: string
  name: string
  displayName: string
}

interface Release {
  id: string
  version: string
  status: string
}

interface EditTranslationDialogProps {
  translationKey: TranslationKey | null
  open: boolean
  onOpenChange: (open: boolean) => void
  namespaces: Namespace[]
  releases: Release[]
  languages: Array<{ code: string; name: string }>
  onSuccess: () => void
}

export default function EditTranslationDialog({
  translationKey,
  open,
  onOpenChange,
  namespaces,
  releases,
  languages,
  onSuccess
}: EditTranslationDialogProps) {
  const { currentUserId } = useCurrentUser()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    key: "",
    description: "",
    namespaceId: "",
    releaseId: "",
    translations: {} as Record<string, string>
  })
  const [isManualPasteDialogOpen, setIsManualPasteDialogOpen] = useState(false)
  const [manualPasteText, setManualPasteText] = useState("")

  // 当翻译键改变时，更新表单数据
  useEffect(() => {
    if (translationKey && open) {
      const translations: Record<string, string> = {}
      languages.forEach(lang => {
        translations[lang.code] = translationKey[lang.code] || ""
      })

      setFormData({
        key: translationKey.key,
        description: translationKey.description || "",
        namespaceId: translationKey.namespace?.id || "",
        releaseId: translationKey.release?.id || "unassigned",
        translations
      })
    }
  }, [translationKey, open, languages])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!translationKey) return

    if (!formData.key.trim()) {
      toast.error("翻译键不能为空")
      return
    }

    if (!formData.namespaceId) {
      toast.error("请选择命名空间")
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/translation-keys/${translationKey.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          key: formData.key.trim(),
          description: formData.description.trim(),
          namespaceId: formData.namespaceId,
          releaseId: formData.releaseId === "unassigned" ? null : formData.releaseId || null,
          translations: formData.translations,
          updatedBy: currentUserId || ""
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast.success("翻译键更新成功")
        onOpenChange(false)
        onSuccess()
      } else {
        toast.error(data.error || "更新失败")
      }
    } catch (error) {
      toast.error("更新翻译键失败")
    } finally {
      setLoading(false)
    }
  }

  const handleTranslationChange = (langCode: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      translations: {
        ...prev.translations,
        [langCode]: value
      }
    }))
  }

  // 粘贴多语言功能
  const handlePasteMultilingual = async () => {
    try {
      // 检查是否支持现代剪贴板API
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        // 剪贴板API不支持，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      // 检查权限
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName })
      if (permission.state === 'denied') {
        // 权限被拒绝，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      const text = await navigator.clipboard.readText()
      if (!text.trim()) {
        // 剪贴板为空，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      // 处理粘贴的文本
      processMultilingualText(text)
    } catch (error) {
      console.error('粘贴多语言失败:', error)
      // 出现错误时，打开手动输入对话框作为备选方案
      setIsManualPasteDialogOpen(true)
    }
  }

  // 处理多语言文本的通用函数
  const processMultilingualText = (text: string) => {
    if (!text.trim()) {
      toast.error("文本为空，请输入多语言内容")
      return
    }

    // 按制表符分割文本
    const parts = text.split('\t')
    
    // 定义语言顺序（与用户Excel中的顺序一致）
    const languageOrder = [
      'en_US',    // 英语
      'zh_TW',    // 繁体中文
      'zh_CN',    // 简体中文
      'fr_FR',    // 法语
      'de_DE',    // 德语
      'ru_RU',    // 俄语
      'es_ES',    // 西班牙语
      'ja_JP',    // 日语
      'th_TH',    // 泰语
      'vi_VN',    // 越南语
      'ko_KR',    // 韩语
      'id_ID',    // 印尼语
      'hi_IN',    // 印地语
      'ur_PK',    // 乌尔都语
      'tr_TR',    // 土耳其语
      'ar_SA',    // 阿拉伯语
      'fa_IR',    // 波斯语
      'pt_PT',    // 葡萄牙语
      'bn_BD'     // 孟加拉语
    ]

    if (parts.length !== languageOrder.length) {
      toast.error(`内容应包含 ${languageOrder.length} 个语言，当前只有 ${parts.length} 个。请确保从Excel复制完整的一行数据。`)
      return
    }

    // 构建新的翻译对象
    const newTranslations: Record<string, string> = {}
    languageOrder.forEach((langCode, index) => {
      const content = parts[index]?.trim()
      if (content) {
        newTranslations[langCode] = content
      }
    })

    // 更新表单
    setFormData(prev => ({
      ...prev,
      translations: {
        ...prev.translations,
        ...newTranslations
      }
    }))

    toast.success(`成功填充 ${Object.keys(newTranslations).length} 个语言的翻译`)
    
    // 关闭手动输入对话框
    setIsManualPasteDialogOpen(false)
    setManualPasteText("")
  }

  // 处理手动输入的多语言文本
  const handleManualPasteSubmit = () => {
    processMultilingualText(manualPasteText)
  }

  const handleAutoTranslate = async (sourceLang: string, targetLang: string) => {
    const sourceText = formData.translations[sourceLang]
    if (!sourceText?.trim()) {
      toast.error("源语言文本为空")
      return
    }

    try {
      const response = await fetch("/api/auto-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          sourceText: sourceText,
          sourceLanguage: sourceLang,
          targetLanguages: [targetLang]
        })
      })

      const data = await response.json()
      
      if (response.ok && data.success && data.translations[targetLang]) {
        handleTranslationChange(targetLang, data.translations[targetLang])
        toast.success(`自动翻译成功: ${sourceLang} → ${targetLang}`)
      } else {
        toast.error(data.error || "自动翻译失败")
      }
    } catch (error) {
      toast.error("自动翻译出错")
    }
  }

  const handleAutoTranslateAll = async () => {
    const sourceText = formData.translations.en_US
    if (!sourceText?.trim()) {
      toast.error("请先输入英文内容")
      return
    }

    setLoading(true)
    const targetLanguages = languages.filter(lang => lang.code !== 'en_US').map(lang => lang.code)

    try {
      const response = await fetch("/api/auto-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          sourceText: sourceText,
          sourceLanguage: 'en_US',
          targetLanguages: targetLanguages
        })
      })

      const data = await response.json()
      
      if (response.ok && data.success && data.translations) {
        let successCount = 0
        let failCount = 0

        // 更新所有翻译结果
        Object.entries(data.translations).forEach(([langCode, translation]) => {
          if (translation && !translation.toString().startsWith('[翻译失败]')) {
            handleTranslationChange(langCode, translation as string)
            successCount++
          } else {
            failCount++
          }
        })

        if (successCount > 0) {
          toast.success(`批量翻译完成: ${successCount} 个成功, ${failCount} 个失败`)
        } else {
          toast.error("批量翻译失败")
        }
      } else {
        toast.error(data.error || "批量翻译失败")
      }
    } catch (error) {
      toast.error("批量翻译出错")
    } finally {
      setLoading(false)
    }
  }

  if (!translationKey) return null

  return (
    <>
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            编辑翻译键
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="namespaceId">命名空间</Label>
              <Select 
                value={formData.namespaceId} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, namespaceId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择命名空间" />
                </SelectTrigger>
                <SelectContent>
                  {namespaces.map((ns) => (
                    <SelectItem key={ns.id} value={ns.id}>
                      {ns.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="releaseId">版本</Label>
              <Select 
                value={formData.releaseId} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, releaseId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择版本(可选)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unassigned">不分配版本</SelectItem>
                  {releases.filter(r => r.status === "DRAFT").map((release) => (
                    <SelectItem key={release.id} value={release.id}>
                      {release.version} (草稿)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="key">翻译键</Label>
            <Input
              id="key"
              placeholder="输入翻译键..."
              value={formData.key}
              onChange={(e) => setFormData(prev => ({ ...prev, key: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Input
              id="description"
              placeholder="输入描述..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          {/* 翻译内容 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium flex items-center gap-2">
                <Globe className="h-5 w-5" />
                翻译内容
              </h4>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handlePasteMultilingual}
                  className="flex items-center gap-2"
                >
                  <Clipboard className="h-4 w-4" />
                  粘贴多语言
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAutoTranslateAll}
                  disabled={loading || !formData.translations.en_US?.trim()}
                  className="flex items-center gap-2"
                >
                  <Wand2 className="h-4 w-4" />
                  翻译所有
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {languages.map((lang) => (
                <div key={lang.code} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor={lang.code}>{lang.name}</Label>
                    <div className="flex gap-1">
                      {lang.code !== 'en_US' && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleAutoTranslate('en_US', lang.code)}
                          className="h-6 px-2 text-xs"
                        >
                          <Wand2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <Textarea
                    id={lang.code}
                    placeholder={`输入${lang.name}翻译...`}
                    value={formData.translations[lang.code] || ""}
                    onChange={(e) => handleTranslationChange(lang.code, e.target.value)}
                    rows={3}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? "更新中..." : "更新翻译键"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>

    {/* 手动粘贴多语言对话框 */}
    <Dialog open={isManualPasteDialogOpen} onOpenChange={setIsManualPasteDialogOpen}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>手动粘贴多语言内容</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="text-sm text-gray-600">
            <p>请将从Excel复制的多语言内容粘贴到下方文本框中。</p>
            <p className="mt-2">内容应按以下顺序排列（用制表符分隔）：</p>
            <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono">
              英语 → 繁体中文 → 简体中文 → 法语 → 德语 → 俄语 → 西班牙语 → 日语 → 泰语 → 越南语 → 韩语 → 印尼语 → 印地语 → 乌尔都语 → 土耳其语 → 阿拉伯语 → 波斯语 → 葡萄牙语 → 孟加拉语
            </div>
          </div>
          
          <div>
            <Label htmlFor="manualPasteText">多语言内容</Label>
            <Textarea
              id="manualPasteText"
              value={manualPasteText}
              onChange={(e) => setManualPasteText(e.target.value)}
              placeholder="请粘贴从Excel复制的多语言内容..."
              className="min-h-[120px] font-mono text-sm"
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsManualPasteDialogOpen(false)
                setManualPasteText("")
              }}
            >
              取消
            </Button>
            <Button
              type="button"
              onClick={handleManualPasteSubmit}
              disabled={!manualPasteText.trim()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              确认填充
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
    </>
  )
}
