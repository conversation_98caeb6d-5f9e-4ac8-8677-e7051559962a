"use client"

import React, { useState, useEffect } from 'react'
import { useCurrentUser } from "@/hooks/use-current-user"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { Plus, Search, Globe, Edit, Save, X, Eye, EyeOff, Wand2, Loader2, Trash2, Clipboard } from "lucide-react"
import EditTranslationDialog from "./EditTranslationDialog"

interface Namespace {
  id: string
  name: string
  displayName: string
  description?: string
}

interface Release {
  id: string
  version: string
  description?: string
  status: string
  createdAt: string
  creator: { username: string }
  _count: { translationKeys: number }
}

interface TranslationKey {
  id: string
  key: string
  namespaceId: string
  description?: string
  createdBy: string
  releaseId: string
  createdAt: string
  updatedAt: string
  namespace: Namespace
  creator: { username: string }
  release?: Release
  // 19种语言的翻译内容
  zh_CN?: string
  zh_TW?: string
  en_US?: string
  ja_JP?: string
  ko_KR?: string
  fr_FR?: string
  de_DE?: string
  es_ES?: string
  pt_PT?: string
  ru_RU?: string
  ar_SA?: string
  bn_BD?: string
  hi_IN?: string
  th_TH?: string
  vi_VN?: string
  id_ID?: string
  tr_TR?: string
  ur_PK?: string
  fa_IR?: string
}

export default function TranslationManagement() {
  const { currentUserId } = useCurrentUser()
  const [translationKeys, setTranslationKeys] = useState<TranslationKey[]>([])
  const [namespaces, setNamespaces] = useState<Namespace[]>([])
  const [releases, setReleases] = useState<Release[]>([])
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState({
    namespace: "",
    release: "",
    key: ""
  })
  const [autoTranslating, setAutoTranslating] = useState(false)
  const [editingTranslationKey, setEditingTranslationKey] = useState<TranslationKey | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingKey, setEditingKey] = useState<TranslationKey | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [isManualPasteDialogOpen, setIsManualPasteDialogOpen] = useState(false)
  const [manualPasteText, setManualPasteText] = useState("")
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [assignToRelease, setAssignToRelease] = useState("")
  const [translationForm, setTranslationForm] = useState({
    namespaceId: "", 
    releaseId: "",
    key: "",
    description: "",
    translations: {} as Record<string, string>
  })

  const languages = [
    { code: "zh_CN", name: "简体中文 (zh_CN)" },
    { code: "zh_TW", name: "繁体中文 (zh_TW)" },
    { code: "en_US", name: "英文 (en_US)" },
    { code: "ja_JP", name: "日文 (ja_JP)" },
    { code: "ko_KR", name: "韩文 (ko_KR)" },
    { code: "fr_FR", name: "法文 (fr_FR)" },
    { code: "de_DE", name: "德文 (de_DE)" },
    { code: "es_ES", name: "西班牙文 (es_ES)" },
    { code: "pt_PT", name: "葡萄牙文 (pt_PT)" },
    { code: "ru_RU", name: "俄文 (ru_RU)" },
    { code: "ar_SA", name: "阿拉伯文 (ar_SA)" },
    { code: "bn_BD", name: "孟加拉文 (bn_BD)" },
    { code: "hi_IN", name: "印地文 (hi_IN)" },
    { code: "th_TH", name: "泰文 (th_TH)" },
    { code: "vi_VN", name: "越南文 (vi_VN)" },
    { code: "id_ID", name: "印尼文 (id_ID)" },
    { code: "tr_TR", name: "土耳其文 (tr_TR)" },
    { code: "ur_PK", name: "乌尔都文 (ur_PK)" },
    { code: "fa_IR", name: "波斯文 (fa_IR)" }
  ]

  useEffect(() => {
    fetchNamespaces()
    fetchReleases()
    fetchTranslationKeys()
  }, [])

  useEffect(() => {
    fetchTranslationKeys()
  }, [filters])





  const fetchNamespaces = async () => {
    try {
      const response = await fetch('/api/namespaces')
      const data = await response.json()
      const namespaces = data.data || data
      setNamespaces(Array.isArray(namespaces) ? namespaces : [])
    } catch (error) {
      console.error('Error fetching namespaces:', error)
      setNamespaces([])
      toast.error("获取命名空间列表失败")
    }
  }

  const fetchReleases = async () => {
    try {
      const response = await fetch('/api/releases')
      const data = await response.json()
      const releases = data.data || data || []
      setReleases(Array.isArray(releases) ? releases : [])
    } catch (error) {
      console.error('Error fetching releases:', error)
      setReleases([])
      toast.error("获取版本列表失败")
    }
  }

  const fetchTranslationKeys = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (filters.namespace) params.append("namespaceId", filters.namespace)
      if (filters.release) params.append("releaseId", filters.release)
      if (filters.key) params.append("search", filters.key)
      
      // 添加所有语言代码参数，确保API返回所有语言字段
      const languageCodes = languages.map(lang => lang.code).join(',')
      params.append("languages", languageCodes)

      const response = await fetch(`/api/translation-keys?${params}`)
      const data = await response.json()
      
      if (response.ok) {
        // API返回的数据结构是 { data: [...], pagination: {...} }
        const keys = data.data || data || []
        setTranslationKeys(Array.isArray(keys) ? keys : [])
      } else {
        console.error('API 错误:', data)
        setTranslationKeys([])
        toast.error(data.error || '获取翻译键列表失败')
      }
    } catch (error) {
      console.error("Error creating translation key:", error)
      toast.error("创建翻译键失败")
    } finally {
      setLoading(false)
    }
  }

  const handleAutoTranslate = async () => {
    const englishText = translationForm.translations.en_US
    if (!englishText) {
      toast.error("请先输入英文翻译作为翻译源")
      return
    }

    setAutoTranslating(true)
    try {
      const targetLanguages = languages
        .filter(lang => lang.code !== "en_US")
        .map(lang => lang.code)

      const response = await fetch("/api/auto-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sourceText: englishText,
          sourceLanguage: "en_US",
          targetLanguages
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setTranslationForm({
          ...translationForm,
          translations: {
            ...translationForm.translations,
            ...data.translations
          }
        })
        toast.success("自动翻译完成！您可以继续编辑这些翻译")
      } else {
        toast.error(data.error || "自动翻译失败")
      }
    } catch (error) {
      toast.error("自动翻译失败")
    } finally {
      setAutoTranslating(false)
    }
  }

  const handleSingleAutoTranslate = async (sourceLang: string, targetLang: string) => {
    const sourceText = translationForm.translations[sourceLang]
    if (!sourceText?.trim()) {
      toast.error("源语言文本为空")
      return
    }

    try {
      const response = await fetch("/api/auto-translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          sourceText: sourceText,
          sourceLanguage: sourceLang,
          targetLanguages: [targetLang]
        })
      })

      const data = await response.json()
      
      if (response.ok && data.success && data.translations[targetLang]) {
        setTranslationForm({
          ...translationForm,
          translations: {
            ...translationForm.translations,
            [targetLang]: data.translations[targetLang]
          }
        })
        toast.success(`自动翻译成功: ${sourceLang} → ${targetLang}`)
      } else {
        toast.error(data.error || "自动翻译失败")
      }
    } catch (error) {
      toast.error("自动翻译出错")
    }
  }

  const handlePasteMultilingual = async () => {
    try {
      // 检查是否支持现代剪贴板API
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        // 剪贴板API不支持，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      // 检查权限
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName })
      if (permission.state === 'denied') {
        // 权限被拒绝，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      const text = await navigator.clipboard.readText()
      if (!text.trim()) {
        // 剪贴板为空，打开手动输入对话框
        setIsManualPasteDialogOpen(true)
        return
      }

      // 处理粘贴的文本
      processMultilingualText(text)
    } catch (error) {
      console.error('粘贴多语言失败:', error)
      // 出现错误时，打开手动输入对话框作为备选方案
      setIsManualPasteDialogOpen(true)
    }
  }

  // 处理多语言文本的通用函数
  const processMultilingualText = (text: string) => {
    if (!text.trim()) {
      toast.error("文本为空，请输入多语言内容")
      return
    }

    // 按制表符分割文本
    const parts = text.split('\t')
    
    // 定义语言顺序（与用户Excel中的顺序一致）
    const languageOrder = [
      'en_US',    // 英语
      'zh_TW',    // 繁体中文
      'zh_CN',    // 简体中文
      'fr_FR',    // 法语
      'de_DE',    // 德语
      'ru_RU',    // 俄语
      'es_ES',    // 西班牙语
      'ja_JP',    // 日语
      'th_TH',    // 泰语
      'vi_VN',    // 越南语
      'ko_KR',    // 韩语
      'id_ID',    // 印尼语
      'hi_IN',    // 印地语
      'ur_PK',    // 乌尔都语
      'tr_TR',    // 土耳其语
      'ar_SA',    // 阿拉伯语
      'fa_IR',    // 波斯语
      'pt_PT',    // 葡萄牙语
      'bn_BD'     // 孟加拉语
    ]

    if (parts.length !== languageOrder.length) {
      toast.error(`内容应包含 ${languageOrder.length} 个语言，当前只有 ${parts.length} 个。请确保从Excel复制完整的一行数据。`)
      return
    }

    // 构建新的翻译对象
    const newTranslations: Record<string, string> = {}
    languageOrder.forEach((langCode, index) => {
      const content = parts[index]?.trim()
      if (content) {
        newTranslations[langCode] = content
      }
    })

    // 更新表单
    setTranslationForm(prev => ({
      ...prev,
      translations: {
        ...prev.translations,
        ...newTranslations
      }
    }))

    toast.success(`成功填充 ${Object.keys(newTranslations).length} 个语言的翻译`)
    
    // 关闭手动输入对话框
    setIsManualPasteDialogOpen(false)
    setManualPasteText("")
  }

  // 处理手动输入的多语言文本
  const handleManualPasteSubmit = () => {
    processMultilingualText(manualPasteText)
  }

  const handleCreateTranslationKey = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // 验证必填字段（版本为可选字段）
      if (!translationForm.namespaceId || !translationForm.key) {
        toast.error("请填写所有必填字段")
        return
      }
      
      // 获取当前用户ID
      let currentUserId
      try {
        const userResponse = await fetch('/api/auth/me')
        if (userResponse.ok) {
          const userData = await userResponse.json()
          currentUserId = userData.id
        }
      } catch (error) {
        console.error('获取当前用户失败:', error)
      }
      
      if (!currentUserId) {
        toast.error("无法获取用户信息，请重新登录")
        return
      }
      
      const formData = {
        ...translationForm,
        releaseId: translationForm.releaseId || null, // 空字符串转换为null
        createdBy: currentUserId || ""
      }
      
      const response = await fetch("/api/translation-keys", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success("翻译键创建成功")
        setIsDialogOpen(false)
        setTranslationForm({ 
          namespaceId: "", 
          releaseId: "",
          key: "", 
          description: "", 
          translations: {} 
        })
        fetchTranslationKeys()
      } else {
        toast.error(data.error || "创建翻译键失败")
      }
    } catch (error) {
      toast.error("创建翻译键出错")
    }
  }



  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-4">


        <div className="flex-1 min-w-40">
          <Label htmlFor="namespace">命名空间</Label>
          <Select value={filters.namespace || "all"} onValueChange={(value) => setFilters({ ...filters, namespace: value === "all" ? "" : value })}>
            <SelectTrigger>
              <SelectValue placeholder="选择命名空间" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部命名空间</SelectItem>
              {Array.isArray(namespaces) && namespaces.map((ns) => (
                <SelectItem key={ns.id} value={ns.id}>
                  {ns.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1 min-w-40">
          <Label htmlFor="release">版本</Label>
          <Select value={filters.release || "all"} onValueChange={(value) => setFilters({ ...filters, release: value === "all" ? "" : value })}>
            <SelectTrigger>
              <SelectValue placeholder="选择版本" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部版本</SelectItem>
              <SelectItem value="unassigned">未分配版本</SelectItem>
              {Array.isArray(releases) && releases.map((release) => (
                <SelectItem key={release.id} value={release.id}>
                  {release.version} ({release.status})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1 min-w-60">
          <Label htmlFor="key">搜索翻译键</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="输入翻译键进行搜索..."
              value={filters.key}
              onChange={(e) => setFilters({ ...filters, key: e.target.value })}
              className="pl-8"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">翻译键管理</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              添加翻译键
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-[95vw] max-h-[95vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>添加翻译键</DialogTitle>
              <DialogDescription>
                创建新的翻译键并为各种语言添加翻译内容
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateTranslationKey} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="namespaceId">命名空间</Label>
                  <Select 
                    value={translationForm.namespaceId} 
                    onValueChange={(value) => setTranslationForm({ ...translationForm, namespaceId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择命名空间" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(namespaces) && namespaces.map((ns) => (
                        <SelectItem key={ns.id} value={ns.id}>
                          {ns.displayName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="releaseId">版本 (可选)</Label>
                  <Select 
                    value={translationForm.releaseId} 
                    onValueChange={(value) => setTranslationForm({ ...translationForm, releaseId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择版本(可选)" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(releases) && releases.filter(r => r.status === "DRAFT").map((release) => (
                        <SelectItem key={release.id} value={release.id}>
                          {release.version} (草稿)
                        </SelectItem>
                      ))}
                      {releases.filter(r => r.status === "DRAFT").length === 0 && (
                        <SelectItem value="none" disabled>
                          没有可用的草稿版本
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="key">翻译键</Label>
                  <Input
                    id="key"
                    placeholder="输入翻译键..."
                    value={translationForm.key}
                    onChange={(e) => setTranslationForm({ ...translationForm, key: e.target.value })}
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">描述</Label>
                <Input
                  id="description"
                  placeholder="输入描述..."
                  value={translationForm.description}
                  onChange={(e) => setTranslationForm({ ...translationForm, description: e.target.value })}
                />
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    多语言翻译
                  </h4>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handlePasteMultilingual}
                      className="flex items-center gap-2"
                    >
                      <Clipboard className="h-4 w-4" />
                      粘贴多语言
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAutoTranslate}
                      disabled={autoTranslating || !translationForm.translations.en_US}
                      className="flex items-center gap-2"
                    >
                      {autoTranslating ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Wand2 className="h-4 w-4" />
                      )}
                      翻译所有
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[65vh] overflow-y-auto p-6 border rounded-lg bg-gray-50">
                  {languages.map((lang) => (
                    <div key={lang.code} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor={lang.code} className="font-medium text-base flex items-center gap-2">
                          {lang.name}
                          {lang.code === "en_US" && (
                            <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">翻译源</span>
                          )}
                        </Label>
                        <div className="flex gap-1">
                          {lang.code !== 'en_US' && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSingleAutoTranslate('en_US', lang.code)}
                              className="h-6 px-2 text-xs"
                            >
                              <Wand2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                      <Textarea
                        id={lang.code}
                        placeholder={lang.code === "en_US" ? "请先输入英文作为翻译源" : `输入${lang.name}翻译...`}
                        value={translationForm.translations[lang.code] || ""}
                        onChange={(e) => setTranslationForm({ 
                          ...translationForm, 
                          translations: { 
                            ...translationForm.translations, 
                            [lang.code]: e.target.value 
                          } 
                        })}
                        rows={3}
                        className="resize-none text-base w-full"
                      />
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  取消
                </Button>
                <Button type="submit">
                  创建翻译键
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* 手动粘贴多语言对话框 */}
        <Dialog open={isManualPasteDialogOpen} onOpenChange={setIsManualPasteDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Clipboard className="h-5 w-5" />
                手动粘贴多语言内容
              </DialogTitle>
              <DialogDescription>
                请将从Excel复制的多语言内容粘贴到下方文本框中。内容应包含19个语言，用制表符分隔。
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="manualPasteText">多语言内容</Label>
                <Textarea
                  id="manualPasteText"
                  placeholder="请粘贴多语言内容，格式：英文内容	繁体中文内容	简体中文内容	..."
                  value={manualPasteText}
                  onChange={(e) => setManualPasteText(e.target.value)}
                  rows={6}
                  className="font-mono text-sm"
                />
              </div>
              
              <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                <p className="font-medium mb-2">语言顺序说明：</p>
                <p className="text-xs leading-relaxed">
                  英语 → 繁体中文 → 简体中文 → 法语 → 德语 → 俄语 → 西班牙语 → 日语 → 泰语 → 越南语 → 韩语 → 印尼语 → 印地语 → 乌尔都语 → 土耳其语 → 阿拉伯语 → 波斯语 → 葡萄牙语 → 孟加拉语
                </p>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsManualPasteDialogOpen(false)
                  setManualPasteText("")
                }}
              >
                取消
              </Button>
              <Button
                type="button"
                onClick={handleManualPasteSubmit}
                disabled={!manualPasteText.trim()}
              >
                确认填充
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-48">翻译键</TableHead>
                <TableHead className="w-40">命名空间</TableHead>
                <TableHead className="w-32">版本</TableHead>
                <TableHead className="w-64">描述</TableHead>
                <TableHead className="w-80">中文翻译</TableHead>
                <TableHead className="w-32">创建者</TableHead>
                <TableHead className="w-32">更新时间</TableHead>
                <TableHead className="w-32">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4">
                    加载中...
                  </TableCell>
                </TableRow>
              ) : translationKeys.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4 text-gray-500">
                    暂无翻译键数据
                  </TableCell>
                </TableRow>
              ) : (
                (Array.isArray(translationKeys) ? translationKeys : []).map((translationKey) => (
                  <TranslationKeyRow
                    key={translationKey.id}
                    translationKey={translationKey}
                    languages={languages}
                    isEditing={false}
                    onEdit={() => {
                      setEditingKey(translationKey)
                      setEditDialogOpen(true)
                    }}
                    onSave={() => {}}
                    onCancel={() => {}}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 编辑翻译键弹窗 */}
      <EditTranslationDialog
        translationKey={editingKey}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        namespaces={Array.isArray(namespaces) ? namespaces : []}
        releases={Array.isArray(releases) ? releases : []}
        languages={languages}
        onSuccess={() => {
          setEditingKey(null)
          fetchTranslationKeys()
        }}
      />
    </div>
  )
}

interface TranslationKeyRowProps {
  translationKey: TranslationKey
  languages: Array<{ code: string; name: string }>
  isEditing: boolean
  onEdit: () => void
  onSave: (translations: Record<string, string>) => void
  onCancel: () => void
}

function TranslationKeyRow({ translationKey, languages, isEditing, onEdit, onSave, onCancel }: TranslationKeyRowProps) {
  const [localTranslations, setLocalTranslations] = useState<Record<string, string>>({})
  const [showAllLanguages, setShowAllLanguages] = useState(false)

  useEffect(() => {
    if (isEditing) {
      const translations: Record<string, string> = {}
      languages.forEach(lang => {
        translations[lang.code] = (translationKey as any)[lang.code] || ""
      })
      setLocalTranslations(translations)
    }
  }, [isEditing, translationKey, languages])

  const handleSave = () => {
    onSave(localTranslations)
  }

  if (isEditing) {
    return (
      <TableRow>
        <TableCell colSpan={8} className="p-0">
          <div className="p-6 bg-gray-50 border-l-4 border-blue-500">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="text-lg font-medium">编辑翻译: {translationKey.key}</h4>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    保存
                  </Button>
                  <Button variant="outline" size="sm" onClick={onCancel}>
                    取消
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4 max-h-[55vh] overflow-y-auto p-6 border rounded-lg bg-gray-50">
                {languages.map((lang) => (
                  <div key={lang.code} className="space-y-2">
                    <Label htmlFor={`edit-${lang.code}`} className="font-medium text-base">{lang.name}</Label>
                    <Textarea
                      id={`edit-${lang.code}`}
                      value={localTranslations[lang.code] || ""}
                      onChange={(e) => setLocalTranslations({ 
                        ...localTranslations, 
                        [lang.code]: e.target.value 
                      })}
                      placeholder={`输入${lang.name}翻译...`}
                      rows={4}
                      className="w-full text-base resize-none min-h-[100px]"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  return (
    <TableRow>
      <TableCell className="font-mono text-sm font-medium">
        {translationKey.key}
      </TableCell>
      <TableCell>{translationKey.namespace.displayName}</TableCell>
      <TableCell>
        {translationKey.release?.version}
      </TableCell>
      <TableCell className="max-w-64">
        <div className="truncate" title={translationKey.description || ""}>
          {translationKey.description || "-"}
        </div>
      </TableCell>
      <TableCell className="max-w-80">
        <div className="truncate text-sm" title={translationKey.zh_CN || ""}>
          {translationKey.zh_CN || "-"}
        </div>
      </TableCell>
      <TableCell>{translationKey.creator.username}</TableCell>
      <TableCell>
        {new Date(translationKey.updatedAt).toLocaleDateString('zh-CN')}
      </TableCell>
      <TableCell>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onEdit}
            disabled={translationKey.release?.status === "PUBLISHED"}
            title={translationKey.release?.status === "PUBLISHED" ? "已发布版本不可编辑" : "编辑翻译键"}
          >
            <Edit className="h-3 w-3" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            disabled={translationKey.release?.status === "PUBLISHED"}
            title={translationKey.release?.status === "PUBLISHED" ? "已发布版本不可删除" : "删除翻译键"}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  )
} 