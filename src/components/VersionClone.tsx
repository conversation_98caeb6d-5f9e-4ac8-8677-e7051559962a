"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"

interface VersionCloneProps {
  sourceReleaseId: string
  sourceVersion: string
  onCloneSuccess?: (newRelease: any) => void
}

export function VersionClone({ sourceReleaseId, sourceVersion, onCloneSuccess }: VersionCloneProps) {
  const [isCloning, setIsCloning] = useState(false)
  const [newVersion, setNewVersion] = useState("")
  const [description, setDescription] = useState("")

  const handleClone = async () => {
    if (!newVersion.trim()) {
      toast.error("请输入新版本号")
      return
    }

    setIsCloning(true)
    try {
      const response = await fetch(`/api/releases/${sourceReleaseId}/clone`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          version: newVersion.trim(),
          description: description.trim() || `基于版本 ${sourceVersion} 创建`,
          createdBy: "cme1m63j700008oqo1spfkhe6" // 使用测试用户ID
        })
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message || "版本克隆成功")
        setNewVersion("")
        setDescription("")
        onCloneSuccess?.(data)
      } else {
        toast.error(data.error || "克隆失败")
      }
    } catch (error) {
      console.error("克隆版本错误:", error)
      toast.error("网络错误，请重试")
    } finally {
      setIsCloning(false)
    }
  }

  return (
    <div className="space-y-4 p-4 border rounded-lg">
      <h3 className="text-lg font-semibold">基于版本 {sourceVersion} 创建新版本</h3>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">新版本号</label>
        <Input
          value={newVersion}
          onChange={(e) => setNewVersion(e.target.value)}
          placeholder="例如: v1.1.0"
          disabled={isCloning}
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium">版本描述（可选）</label>
        <Textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="描述这个版本的变更内容..."
          disabled={isCloning}
          rows={3}
        />
      </div>

      <div className="flex gap-2">
        <Button
          onClick={handleClone}
          disabled={isCloning || !newVersion.trim()}
          className="flex-1"
        >
          {isCloning ? "克隆中..." : "创建新版本"}
        </Button>
      </div>

      <div className="text-sm text-gray-600">
        <p>• 新版本将复制源版本的所有翻译键</p>
        <p>• 新版本状态为草稿，可以自由编辑</p>
        <p>• 源版本保持不变</p>
      </div>
    </div>
  )
}
