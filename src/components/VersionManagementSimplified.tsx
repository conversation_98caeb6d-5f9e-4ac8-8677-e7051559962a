"use client"

import React, { useState, useEffect } from 'react'
import { useCurrentUser } from "@/hooks/use-current-user"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { Plus, Edit, Archive, Copy, Trash2, GitBranch, GitCompare } from "lucide-react"
import VersionDiff from "./VersionDiff"
import CloneConfirmation from "./CloneConfirmation"
import TranslationTestTool from "./TranslationTestTool"
import { smartCacheInvalidation } from "@/lib/cache-invalidation"

interface Namespace {
  id: string
  name: string
  displayName: string
  description?: string
}

interface Release {
  id: string
  version: string
  description?: string
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
  namespaceId: string
  namespace: {
    id: string
    name: string
    displayName: string
  }
  publishedAt?: string
  createdAt: string
  updatedAt: string
  creator: {
    id: string
    username: string
  }
  _count: {
    translationKeys: number
  }
}

interface VersionManagementSimplifiedProps {
  namespaceId: string
}

export default function VersionManagementSimplified({ namespaceId }: VersionManagementSimplifiedProps) {
  const { currentUserId } = useCurrentUser()
  const [releases, setReleases] = useState<Release[]>([])
  const [namespaces, setNamespaces] = useState<Namespace[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingRelease, setEditingRelease] = useState<Release | null>(null)
  
  // 筛选条件
  const [filterNamespace, setFilterNamespace] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterVersion, setFilterVersion] = useState("")

  // 表单数据
  const [releaseForm, setReleaseForm] = useState<{
    namespaceId: string
    version: string
    description: string
    status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  }>({
    namespaceId: "",
    version: "",
    description: "",
    status: "DRAFT"
  })

  useEffect(() => {
    fetchReleases()
    fetchNamespaces()
  }, [])

  const fetchReleases = async () => {
    try {
      const response = await fetch("/api/releases")
      const data = await response.json()
      setReleases(data.data || [])
    } catch (error) {
      toast.error("获取版本列表失败")
    } finally {
      setLoading(false)
    }
  }

  const fetchNamespaces = async () => {
    try {
      const response = await fetch("/api/namespaces")
      const data = await response.json()
      setNamespaces(data.data || [])
    } catch (error) {
      toast.error("获取命名空间列表失败")
    }
  }

  const handleCreateRelease = async () => {
    if (!releaseForm.namespaceId || !releaseForm.version) {
      toast.error("请填写必填字段")
      return
    }

    try {
      const response = await fetch("/api/releases", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...releaseForm,
          createdBy: currentUserId || ""
        }),
      })

      if (response.ok) {
        toast.success("版本创建成功")
        setShowCreateDialog(false)
        setReleaseForm({
          namespaceId: "",
          version: "",
          description: "",
          status: "DRAFT"
        })
        fetchReleases()
      } else {
        const error = await response.json()
        toast.error(error.error || "创建失败")
      }
    } catch (error) {
      toast.error("创建版本失败")
    }
  }

  const handleUpdateRelease = async () => {
    if (!editingRelease) return

    try {
      const response = await fetch(`/api/releases/${editingRelease.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(releaseForm),
      })

      if (response.ok) {
        toast.success("版本更新成功")
        setShowEditDialog(false)
        setEditingRelease(null)
        fetchReleases()
      } else {
        const error = await response.json()
        toast.error(error.error || "更新失败")
      }
    } catch (error) {
      toast.error("更新版本失败")
    }
  }

  const handlePublishRelease = async (releaseId: string) => {
    try {
      const response = await fetch(`/api/releases/${releaseId}/publish`, {
        method: "POST",
      })

      if (response.ok) {
        toast.success("版本发布成功")
        fetchReleases()
      } else {
        const error = await response.json()
        toast.error(error.error || "发布失败")
      }
    } catch (error) {
      toast.error("发布版本失败")
    }
  }

  const handleArchiveRelease = async (releaseId: string) => {
    try {
      const response = await fetch(`/api/releases/${releaseId}/archive`, {
        method: "POST",
      })

      if (response.ok) {
        toast.success("版本归档成功")
        fetchReleases()
      } else {
        const error = await response.json()
        toast.error(error.error || "归档失败")
      }
    } catch (error) {
      toast.error("归档版本失败")
    }
  }



  const handleDeleteRelease = async (releaseId: string) => {
    if (!confirm("确定要删除此版本吗？此操作不可撤销。")) {
      return
    }

    try {
      const response = await fetch(`/api/releases/${releaseId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast.success("版本删除成功")
        fetchReleases()
      } else {
        const error = await response.json()
        toast.error(error.error || "删除失败")
      }
    } catch (error) {
      toast.error("删除版本失败")
    }
  }

  const openEditDialog = (release: Release) => {
    setEditingRelease(release)
    setReleaseForm({
      namespaceId: release.namespaceId,
      version: release.version,
      description: release.description || "",
      status: release.status
    })
    setShowEditDialog(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "DRAFT":
        return <Badge variant="secondary">草稿</Badge>
      case "PUBLISHED":
        return <Badge variant="default">已发布</Badge>
      case "ARCHIVED":
        return <Badge variant="outline">已归档</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // 筛选版本
  const filteredReleases = releases.filter(release => {
    if (filterNamespace && filterNamespace !== "all" && release.namespaceId !== filterNamespace) return false
    if (filterStatus && filterStatus !== "all" && release.status !== filterStatus) return false
    if (filterVersion && !release.version.toLowerCase().includes(filterVersion.toLowerCase())) return false
    return true
  })

  if (loading) {
    return <div className="flex justify-center items-center h-64">加载中...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">版本管理</h2>
        <div className="flex gap-2">
          <TranslationTestTool releases={releases} />
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                创建版本
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>创建新版本</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">命名空间 *</label>
                  <Select 
                    value={releaseForm.namespaceId} 
                    onValueChange={(value) => setReleaseForm({ ...releaseForm, namespaceId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择命名空间" />
                    </SelectTrigger>
                  <SelectContent>
                    {namespaces.map((ns) => (
                      <SelectItem key={ns.id} value={ns.id}>
                        {ns.displayName} ({ns.name})
                      </SelectItem>
                    ))}
                  </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">版本号 *</label>
                  <Input
                    value={releaseForm.version}
                    onChange={(e) => setReleaseForm({ ...releaseForm, version: e.target.value })}
                    placeholder="例如: v1.0.0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">描述</label>
                  <Textarea
                    value={releaseForm.description}
                    onChange={(e) => setReleaseForm({ ...releaseForm, description: e.target.value })}
                    placeholder="版本描述..."
                    />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">状态</label>
                  <Select 
                    value={releaseForm.status} 
                    onValueChange={(value) => setReleaseForm({ ...releaseForm, status: value as 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DRAFT">草稿</SelectItem>
                      <SelectItem value="PUBLISHED">已发布</SelectItem>
                      <SelectItem value="ARCHIVED">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      取消
                    </Button>
                    <Button onClick={handleCreateRelease}>
                      创建
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

      {/* 筛选条件 */}
      <div className="flex space-x-4">
        <Select value={filterNamespace} onValueChange={setFilterNamespace}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="筛选命名空间" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部命名空间</SelectItem>
            {namespaces.map((ns) => (
              <SelectItem key={ns.id} value={ns.id}>
                {ns.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="筛选状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="DRAFT">草稿</SelectItem>
            <SelectItem value="PUBLISHED">已发布</SelectItem>
            <SelectItem value="ARCHIVED">已归档</SelectItem>
          </SelectContent>
        </Select>

        <Input
          placeholder="搜索版本号..."
          value={filterVersion}
          onChange={(e) => setFilterVersion(e.target.value)}
          className="w-48"
        />
      </div>

      {/* 版本列表 */}
      <div className="grid gap-4">
        {filteredReleases.map((release) => (
          <Card key={release.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <GitBranch className="w-5 h-5" />
                    <span>{release.version}</span>
                    {getStatusBadge(release.status)}
                  </CardTitle>
                  <div className="text-sm text-muted-foreground mt-1">
                    {release.namespace?.displayName || '全局版本'} • {release._count.translationKeys} 个翻译键
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditDialog(release)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  
                  {release.status === "DRAFT" && (
                    <>
                      <VersionDiff 
                        releaseId={release.id}
                        trigger={
                          <Button variant="outline" size="sm">
                            <GitCompare className="w-4 h-4" />
                          </Button>
                        }
                      />
                      <VersionDiff 
                        releaseId={release.id}
                        mode="publish"
                        onPublishConfirm={() => handlePublishRelease(release.id)}
                        trigger={
                          <Button
                            variant="outline"
                            size="sm"
                          >
                            发布
                          </Button>
                        }
                      />
                    </>
                  )}
                  
                  {release.status === "PUBLISHED" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleArchiveRelease(release.id)}
                    >
                      <Archive className="w-4 h-4" />
                    </Button>
                  )}
                  
                  <CloneConfirmation
                    sourceRelease={release}
                    onCloneSuccess={fetchReleases}
                    trigger={
                      <Button variant="outline" size="sm">
                        <Copy className="w-4 h-4" />
                      </Button>
                    }
                  />
                  
                  {release.status === "DRAFT" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteRelease(release.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            {release.description && (
              <CardContent>
                <p className="text-sm text-muted-foreground">{release.description}</p>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {filteredReleases.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          暂无版本数据
        </div>
      )}

      {/* 编辑对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑版本</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">命名空间 *</label>
              <Select 
                value={releaseForm.namespaceId} 
                onValueChange={(value) => setReleaseForm({ ...releaseForm, namespaceId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择命名空间" />
                </SelectTrigger>
                <SelectContent>
                  {namespaces.map((ns) => (
                    <SelectItem key={ns.id} value={ns.id}>
                      {ns.displayName} ({ns.name})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">版本号 *</label>
              <Input
                value={releaseForm.version}
                onChange={(e) => setReleaseForm({ ...releaseForm, version: e.target.value })}
                placeholder="例如: v1.0.0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">描述</label>
              <Textarea
                value={releaseForm.description}
                onChange={(e) => setReleaseForm({ ...releaseForm, description: e.target.value })}
                placeholder="版本描述..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">状态</label>
              <Select 
                value={releaseForm.status} 
                onValueChange={(value) => setReleaseForm({ ...releaseForm, status: value as 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">草稿</SelectItem>
                  <SelectItem value="PUBLISHED">已发布</SelectItem>
                  <SelectItem value="ARCHIVED">已归档</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                取消
              </Button>
              <Button onClick={handleUpdateRelease}>
                更新
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}


