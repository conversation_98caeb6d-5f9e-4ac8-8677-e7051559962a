"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { Eye, Copy, ExternalLink, Code, Globe } from "lucide-react"

interface Namespace {
  id: string
  name: string
  displayName: string
}

interface Release {
  id: string
  version: string
  status: string
  namespace: Namespace
}

interface TranslationPreviewProps {
  namespaces: Namespace[]
  releases: Release[]
}

export default function TranslationPreview({ namespaces, releases }: TranslationPreviewProps) {
  const [selectedNamespace, setSelectedNamespace] = useState("")
  const [selectedVersion, setSelectedVersion] = useState("")
  const [selectedLanguage, setSelectedLanguage] = useState("zh_CN")
  const [previewData, setPreviewData] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [apiUrl, setApiUrl] = useState("")

  const languages = [
    { code: "zh_CN", name: "简体中文" },
    { code: "zh_TW", name: "繁体中文" },
    { code: "en_US", name: "英语" },
    { code: "ja_JP", name: "日语" },
    { code: "ko_KR", name: "韩语" },
    { code: "fr_FR", name: "法语" },
    { code: "de_DE", name: "德语" },
    { code: "es_ES", name: "西班牙语" },
    { code: "pt_PT", name: "葡萄牙语" },
    { code: "ru_RU", name: "俄语" }
  ]

  // 过滤选中命名空间的版本
  const filteredReleases = releases.filter(r => r.namespace.id === selectedNamespace)

  // 生成API调用URL
  useEffect(() => {
    if (selectedNamespace && selectedVersion && selectedLanguage) {
      const namespace = namespaces.find(n => n.id === selectedNamespace)
      const version = releases.find(r => r.id === selectedVersion)
      
      if (namespace && version) {
        // 这里使用Go服务的API格式
        const baseUrl = process.env.NEXT_PUBLIC_I18N_SERVICE_URL || "http://localhost:8080"
        setApiUrl(`${baseUrl}/api/v1/translations/${namespace.name}?version=${version.version}&lang=${selectedLanguage}`)
      }
    }
  }, [selectedNamespace, selectedVersion, selectedLanguage, namespaces, releases])

  const handlePreview = async () => {
    if (!selectedNamespace || !selectedVersion || !selectedLanguage) {
      toast.error("请选择命名空间、版本和语言")
      return
    }

    setLoading(true)
    try {
      // 这里模拟调用Go服务API
      // 实际部署时需要配置正确的Go服务地址
      const response = await fetch(apiUrl)
      
      if (response.ok) {
        const data = await response.json()
        setPreviewData(data.data?.translations || {})
        toast.success("预览加载成功")
      } else {
        // 如果Go服务不可用，从数据库直接查询作为备用
        const fallbackResponse = await fetch(`/api/translation-keys?namespaceId=${selectedNamespace}&releaseId=${selectedVersion}`)
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json()
          const translations: Record<string, string> = {}
          
          fallbackData.data?.forEach((key: any) => {
            if (key[selectedLanguage]) {
              translations[key.key] = key[selectedLanguage]
            }
          })
          
          setPreviewData(translations)
          toast.success("预览加载成功（备用模式）")
        } else {
          throw new Error("预览加载失败")
        }
      }
    } catch (error) {
      console.error("预览错误:", error)
      toast.error("预览加载失败，请检查Go服务是否运行")
    } finally {
      setLoading(false)
    }
  }

  const copyApiUrl = () => {
    navigator.clipboard.writeText(apiUrl)
    toast.success("API URL已复制到剪贴板")
  }

  const copyAsJson = () => {
    navigator.clipboard.writeText(JSON.stringify(previewData, null, 2))
    toast.success("翻译数据已复制为JSON格式")
  }

  const selectedNamespaceObj = namespaces.find(n => n.id === selectedNamespace)
  const selectedVersionObj = releases.find(r => r.id === selectedVersion)

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Eye className="h-4 w-4" />
          翻译预览
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            翻译内容预览 & 测试
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 选择器 */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>命名空间</Label>
              <Select value={selectedNamespace} onValueChange={setSelectedNamespace}>
                <SelectTrigger>
                  <SelectValue placeholder="选择命名空间" />
                </SelectTrigger>
                <SelectContent>
                  {namespaces.map((ns) => (
                    <SelectItem key={ns.id} value={ns.id}>
                      {ns.displayName} ({ns.name})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>版本</Label>
              <Select value={selectedVersion} onValueChange={setSelectedVersion}>
                <SelectTrigger>
                  <SelectValue placeholder="选择版本" />
                </SelectTrigger>
                <SelectContent>
                  {filteredReleases.map((release) => (
                    <SelectItem key={release.id} value={release.id}>
                      <div className="flex items-center gap-2">
                        {release.version}
                        <Badge variant={release.status === "PUBLISHED" ? "default" : "secondary"}>
                          {release.status === "PUBLISHED" ? "已发布" : "草稿"}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>语言</Label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* API URL显示 */}
          {apiUrl && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Code className="h-4 w-4" />
                  API调用地址
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Input value={apiUrl} readOnly className="font-mono text-xs" />
                  <Button variant="outline" size="sm" onClick={copyApiUrl}>
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.open(apiUrl, '_blank')}>
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  开发者可以使用此URL在应用中测试{selectedVersionObj?.status === "PUBLISHED" ? "已发布" : "草稿"}版本的翻译内容
                </p>
              </CardContent>
            </Card>
          )}

          {/* 预览按钮 */}
          <div className="flex gap-2">
            <Button onClick={handlePreview} disabled={loading || !selectedNamespace || !selectedVersion}>
              {loading ? "加载中..." : "预览翻译内容"}
            </Button>
            {Object.keys(previewData).length > 0 && (
              <Button variant="outline" onClick={copyAsJson}>
                <Copy className="h-4 w-4 mr-2" />
                复制JSON
              </Button>
            )}
          </div>

          {/* 预览结果 */}
          {Object.keys(previewData).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">
                  翻译内容预览 
                  <Badge className="ml-2">
                    {Object.keys(previewData).length} 个翻译键
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-96 overflow-y-auto">
                  <div className="grid gap-2">
                    {Object.entries(previewData).map(([key, value]) => (
                      <div key={key} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <code className="text-xs bg-white px-2 py-1 rounded border font-mono text-blue-600 min-w-0 flex-shrink-0">
                          {key}
                        </code>
                        <span className="text-sm flex-1 min-w-0 break-words">
                          {value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">草稿版本测试指南</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <div><strong>开发环境测试：</strong>在应用中使用上述API URL替换生产环境的翻译接口</div>
              <div><strong>A/B测试：</strong>根据用户标识动态选择version参数</div>
              <div><strong>QA测试：</strong>配置环境变量指定测试版本</div>
              <div><strong>预发布验证：</strong>在预发布环境使用草稿版本进行最终验证</div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
