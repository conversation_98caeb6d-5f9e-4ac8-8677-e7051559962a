"use client"

import { useState } from "react"
import { useCurrentUser } from "@/hooks/use-current-user"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { Copy } from "lucide-react"

interface CloneConfirmationProps {
  sourceRelease: {
    id: string
    version: string
    description?: string
    _count: { translationKeys: number }
    namespace?: { displayName: string }
  }
  trigger?: React.ReactNode
  onCloneSuccess?: () => void
}

export default function CloneConfirmation({ sourceRelease, trigger, onCloneSuccess }: CloneConfirmationProps) {
  const { currentUserId } = useCurrentUser()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    version: `${sourceRelease.version}-copy`,
    description: `基于版本 ${sourceRelease.version} 创建`
  })

  const handleClone = async () => {
    if (!formData.version.trim()) {
      toast.error("版本号不能为空")
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/releases/${sourceRelease.id}/clone`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          version: formData.version.trim(),
          description: formData.description.trim(),
          createdBy: currentUserId || ""
        })
      })

      const data = await response.json()

      if (response.ok) {
        // 显示详细的克隆结果
        if (data.cloneStats?.skippedKeys > 0) {
          toast.success(
            `${data.message}\n跳过的翻译键: ${data.cloneStats.skippedKeysList.slice(0, 3).join(', ')}${data.cloneStats.skippedKeysList.length > 3 ? '...' : ''}`,
            { duration: 6000 }
          )
        } else {
          toast.success(data.message || "版本克隆成功")
        }
        
        setOpen(false)
        onCloneSuccess?.()
        // 重置表单
        setFormData({
          version: `${sourceRelease.version}-copy`,
          description: `基于版本 ${sourceRelease.version} 创建`
        })
      } else {
        toast.error(data.error || "克隆失败")
      }
    } catch (error) {
      toast.error("克隆版本失败")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Copy className="w-4 h-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>克隆版本</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 源版本信息 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium">源版本信息</div>
            <div className="text-sm text-gray-600 mt-1">
              版本: {sourceRelease.version}
            </div>
            <div className="text-sm text-gray-600">
              命名空间: {sourceRelease.namespace?.displayName || '全局'}
            </div>
            <div className="text-sm text-gray-600">
              翻译键数量: {sourceRelease._count.translationKeys} 个
            </div>
          </div>

          {/* 新版本信息 */}
          <div className="space-y-3">
            <div>
              <Label htmlFor="version">新版本号 *</Label>
              <Input
                id="version"
                value={formData.version}
                onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                placeholder="输入新版本号"
              />
            </div>
            
            <div>
              <Label htmlFor="description">版本描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="输入版本描述"
                rows={3}
              />
            </div>
          </div>

          {/* 警告信息 */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="text-sm font-medium text-blue-800">克隆规则</div>
            <div className="text-sm text-blue-700 mt-1">
              • 只复制不冲突的翻译键到新版本<br/>
              • 如果翻译键已存在，将跳过不复制<br/>
              • 新版本状态为草稿<br/>
              • 克隆完成后会显示详细的复制结果
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button
              onClick={handleClone}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? "克隆中..." : "确认克隆"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
