"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"
import { Plus, Settings, Database, Globe, Trash2, Edit } from "lucide-react"

interface Application {
  id: string
  name: string
  displayName: string
  description?: string
  createdAt: string
  updatedAt: string
  _count: {
    namespaces: number
    environments: number
    keys: number
  }
}





const ApplicationManagement: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedApp, setSelectedApp] = useState<Application | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: ''
  })
  


  // 获取应用列表
  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/applications')
      if (response.ok) {
        const result = await response.json()
        
        // 支持新的分页格式
        if (result.data && Array.isArray(result.data)) {
          setApplications(result.data)
        } else if (Array.isArray(result)) {
          // 兼容旧格式
          setApplications(result)
        } else {
          setApplications([])
        }
      } else {
        toast({
          title: "错误",
          description: "获取应用列表失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('获取应用列表失败:', error)
      toast({
        title: "错误",
        description: "获取应用列表失败",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchApplications()
  }, [])

  // 创建应用
  const handleCreateApplication = async () => {
    try {
      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast({
          title: "成功",
          description: "应用创建成功",
        })
        setCreateDialogOpen(false)
        setFormData({ name: '', displayName: '', description: '' })
        fetchApplications()
      } else {
        const error = await response.json()
        toast({
          title: "错误",
          description: error.error || "创建应用失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('创建应用失败:', error)
      toast({
        title: "错误",
        description: "创建应用失败",
        variant: "destructive",
      })
    }
  }

  // 更新应用
  const handleUpdateApplication = async () => {
    if (!selectedApp) return

    try {
      const response = await fetch(`/api/applications/${selectedApp.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayName: formData.displayName,
          description: formData.description
        }),
      })

      if (response.ok) {
        toast({
          title: "成功",
          description: "应用更新成功",
        })
        setEditDialogOpen(false)
        setSelectedApp(null)
        setFormData({ name: '', displayName: '', description: '' })
        fetchApplications()
      } else {
        const error = await response.json()
        toast({
          title: "错误",
          description: error.error || "更新应用失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('更新应用失败:', error)
      toast({
        title: "错误",
        description: "更新应用失败",
        variant: "destructive",
      })
    }
  }

  // 删除应用
  const handleDeleteApplication = async (app: Application) => {
    if (!confirm(`确定要删除应用 "${app.displayName}" 吗？这将删除所有相关数据！`)) {
      return
    }

    try {
      const response = await fetch(`/api/applications/${app.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: "成功",
          description: "应用删除成功",
        })
        fetchApplications()
      } else {
        const error = await response.json()
        toast({
          title: "错误",
          description: error.error || "删除应用失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('删除应用失败:', error)
      toast({
        title: "错误",
        description: "删除应用失败",
        variant: "destructive",
      })
    }
  }

  // 打开编辑对话框
  const openEditDialog = (app: Application) => {
    setSelectedApp(app)
    setFormData({
      name: app.name,
      displayName: app.displayName,
      description: app.description || ''
    })
    setEditDialogOpen(true)
  }





  if (loading) {
    return <div className="flex justify-center items-center h-64">加载中...</div>
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">应用管理</h2>
          <p className="text-gray-600">管理微服务应用和翻译内容</p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              创建应用
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>创建新应用</DialogTitle>
              <DialogDescription>
创建一个新的微服务应用，系统将自动创建默认命名空间。
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  应用名称
                </Label>
                <Input
                  id="name"
                  placeholder="user-service"
                  className="col-span-3"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="displayName" className="text-right">
                  显示名称
                </Label>
                <Input
                  id="displayName"
                  placeholder="用户服务"
                  className="col-span-3"
                  value={formData.displayName}
                  onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="description"
                  placeholder="应用描述..."
                  className="col-span-3"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleCreateApplication}>
                创建应用
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 应用列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {applications.map((app) => (
          <Card key={app.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{app.displayName}</CardTitle>
                  <CardDescription className="font-mono text-sm">{app.name}</CardDescription>
                </div>
                <div className="flex space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openEditDialog(app)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteApplication(app)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              {app.description && (
                <p className="text-sm text-gray-600">{app.description}</p>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 统计信息 */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-blue-600">{app._count.namespaces}</div>
                    <div className="text-gray-500">命名空间</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-purple-600">0</div>
                    <div className="text-gray-500">翻译键</div>
                  </div>
                </div>
                

                
                {/* 创建时间 */}
                <div className="text-xs text-gray-400">
                  创建于: {new Date(app.createdAt).toLocaleDateString('zh-CN')}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {applications.length === 0 && (
        <div className="text-center py-12">
          <Settings className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无应用</h3>
          <p className="text-gray-600 mb-4">创建第一个微服务应用开始管理i18n配置</p>
          <Button onClick={() => setCreateDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            创建应用
          </Button>
        </div>
      )}

      {/* 编辑对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑应用</DialogTitle>
            <DialogDescription>
              修改应用的显示名称和描述信息。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                应用名称
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                disabled
                className="col-span-3 bg-gray-50"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-displayName" className="text-right">
                显示名称
              </Label>
              <Input
                id="edit-displayName"
                placeholder="用户服务"
                className="col-span-3"
                value={formData.displayName}
                onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                描述
              </Label>
              <Textarea
                id="edit-description"
                placeholder="应用描述..."
                className="col-span-3"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleUpdateApplication}>
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  )
}

export default ApplicationManagement
