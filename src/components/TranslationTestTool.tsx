"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { toast } from "sonner"
import { Eye, Copy, ExternalLink, Code, TestTube } from "lucide-react"

interface Release {
  id: string
  version: string
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED"
}

interface TranslationTestToolProps {
  releases: Release[]
  namespaceName?: string
}

export default function TranslationTestTool({ releases, namespaceName = "auth" }: TranslationTestToolProps) {
  const [selectedVersion, setSelectedVersion] = useState("")
  const [selectedLanguage, setSelectedLanguage] = useState("all")
  const [customNamespace, setCustomNamespace] = useState(namespaceName)
  const [testResults, setTestResults] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  const languages = [
    { code: "all", name: "所有语言" },
    { code: "zh_CN", name: "简体中文" },
    { code: "en_US", name: "英语" },
    { code: "ja_JP", name: "日语" },
    { code: "ko_KR", name: "韩语" }
  ]

  // 生成测试API URL
  const generateApiUrl = (version?: string) => {
    const baseUrl = process.env.NEXT_PUBLIC_I18N_SERVICE_URL || "http://localhost:8080"
    const params = new URLSearchParams()
    
    if (version) {
      params.append('version', version)
    } else {
      params.append('env', 'prod')
    }
    
    // 只有选择了具体语言才添加lang参数
    if (selectedLanguage !== "all") {
      params.append('lang', selectedLanguage)
    }
    
    return `${baseUrl}/api/v1/translations/${customNamespace}?${params.toString()}`
  }

  const testVersion = async (version?: string) => {
    setLoading(true)
    try {
      const apiUrl = generateApiUrl(version)
      console.log("测试API:", apiUrl)
      
      // 模拟API调用（实际部署时会调用真实的Go服务）
      let mockData: Record<string, string> = {}
      
      if (selectedLanguage === "all") {
        // 返回多语言数据
        mockData = {
          "login.title": `登录 | Login | ログイン${version ? ` (${version})` : ""}`,
          "login.username": "用户名 | Username | ユーザー名",
          "login.password": "密码 | Password | パスワード",
          "login.submit": "登录 | Login | ログイン",
          "common.cancel": "取消 | Cancel | キャンセル",
          "common.confirm": "确认 | Confirm | 確認"
        }
      } else {
        // 返回单语言数据
        const langTexts = {
          "zh_CN": {
            "login.title": version ? `登录 (${version})` : "登录",
            "login.username": "用户名",
            "login.password": "密码",
            "login.submit": "登录",
            "common.cancel": "取消",
            "common.confirm": "确认"
          },
          "en_US": {
            "login.title": version ? `Login (${version})` : "Login",
            "login.username": "Username",
            "login.password": "Password",
            "login.submit": "Login",
            "common.cancel": "Cancel",
            "common.confirm": "Confirm"
          },
          "ja_JP": {
            "login.title": version ? `ログイン (${version})` : "ログイン",
            "login.username": "ユーザー名",
            "login.password": "パスワード",
            "login.submit": "ログイン",
            "common.cancel": "キャンセル",
            "common.confirm": "確認"
          }
        }
        mockData = langTexts[selectedLanguage as keyof typeof langTexts] || langTexts["zh_CN"]
      }
      
      setTestResults(mockData)
      const langText = selectedLanguage === "all" ? "所有语言" : languages.find(l => l.code === selectedLanguage)?.name
      toast.success(`${version ? '草稿版本' : '生产版本'}测试成功 (${langText})`)
    } catch (error) {
      console.error("测试失败:", error)
      toast.error("测试失败，请检查Go服务")
    } finally {
      setLoading(false)
    }
  }

  const copyApiUrl = (version?: string) => {
    const url = generateApiUrl(version)
    navigator.clipboard.writeText(url)
    toast.success("API URL已复制")
  }

  const copyAsCode = (version?: string) => {
    const url = generateApiUrl(version)
    const code = `// ${version ? '草稿版本' : '生产版本'}测试
const translations = await fetch('${url}')
const data = await translations.json()
console.log(data)`
    
    navigator.clipboard.writeText(code)
    toast.success("测试代码已复制")
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <TestTube className="h-4 w-4" />
          草稿测试
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            草稿版本测试工具
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 配置区域 */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>命名空间</Label>
              <Input 
                value={customNamespace} 
                onChange={(e) => setCustomNamespace(e.target.value)}
                placeholder="例如: auth, common"
              />
            </div>
            <div className="space-y-2">
              <Label>语言 <span className="text-xs text-muted-foreground">(可选，不选择将返回所有语言)</span></Label>
              <select 
                value={selectedLanguage} 
                onChange={(e) => setSelectedLanguage(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                {languages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label>测试类型</Label>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => testVersion()}
                  disabled={loading}
                >
                  生产版本
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => testVersion(selectedVersion)}
                  disabled={loading || !selectedVersion}
                >
                  草稿版本
                </Button>
              </div>
            </div>
          </div>

          {/* 版本选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">选择草稿版本进行测试</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {releases.filter(r => r.status === "DRAFT").map((release) => (
                  <div 
                    key={release.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedVersion === release.version 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedVersion(release.version)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{release.version}</span>
                      <Badge variant="secondary">草稿</Badge>
                    </div>
                  </div>
                ))}
              </div>
              {releases.filter(r => r.status === "DRAFT").length === 0 && (
                <p className="text-sm text-muted-foreground">暂无草稿版本</p>
              )}
            </CardContent>
          </Card>

          {/* API调用示例 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Code className="h-4 w-4" />
                API调用示例
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* 生产版本API */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs">生产环境API</Label>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => copyApiUrl()}>
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => copyAsCode()}>
                      <Code className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <Input 
                  value={generateApiUrl()} 
                  readOnly 
                  className="font-mono text-xs"
                />
              </div>

              {/* 草稿版本API */}
              {selectedVersion && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">草稿版本API ({selectedVersion})</Label>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" onClick={() => copyApiUrl(selectedVersion)}>
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => copyAsCode(selectedVersion)}>
                        <Code className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <Input 
                    value={generateApiUrl(selectedVersion)} 
                    readOnly 
                    className="font-mono text-xs"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* 测试结果 */}
          {Object.keys(testResults).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">
                  测试结果 
                  <Badge className="ml-2">
                    {Object.keys(testResults).length} 个翻译键
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {Object.entries(testResults).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                      <code className="text-xs bg-white px-2 py-1 rounded border text-blue-600 min-w-0 flex-shrink-0">
                        {key}
                      </code>
                      <span className="text-sm flex-1">{value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">使用说明</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <div><strong>1. 选择草稿版本：</strong>点击上方草稿版本卡片进行选择</div>
              <div><strong>2. 选择语言：</strong>
                <ul className="ml-4 mt-1 space-y-1">
                  <li>• <strong>所有语言</strong>：返回所有语言的翻译（推荐用于全面测试）</li>
                  <li>• <strong>特定语言</strong>：只返回选中语言的翻译（推荐用于单语言应用）</li>
                </ul>
              </div>
              <div><strong>3. 测试对比：</strong>分别测试生产版本和草稿版本，对比翻译差异</div>
              <div><strong>4. 复制API：</strong>复制API地址或代码，在应用中进行集成测试</div>
              <div><strong>5. 环境配置：</strong>在测试环境中配置环境变量指定草稿版本</div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
