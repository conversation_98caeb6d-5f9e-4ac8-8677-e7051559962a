"use client"

import { useState } from "react"
import { signOut, useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { LogOut, Languages, Rocket, Database } from "lucide-react"
import NamespaceManagement from "./NamespaceManagement"
import TranslationManagement from "./TranslationManagement"
import VersionManagementSimplified from "./VersionManagementSimplified"
import VersionAssignment from "./VersionAssignment"

export default function Dashboard() {
  const { data: session } = useSession()

  const handleLogout = () => {
    signOut({ callbackUrl: "/login" })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">i18n 管理系统</h1>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">欢迎，{session?.user?.username}</span>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                退出登录
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <Tabs defaultValue="namespaces" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="namespaces" className="flex items-center">
                <Database className="h-4 w-4 mr-2" />
                命名空间
              </TabsTrigger>
              <TabsTrigger value="translations" className="flex items-center">
                <Languages className="h-4 w-4 mr-2" />
                翻译管理
              </TabsTrigger>
              <TabsTrigger value="releases" className="flex items-center">
                <Rocket className="h-4 w-4 mr-2" />
                版本发布
              </TabsTrigger>
              <TabsTrigger value="assignment" className="flex items-center">
                <Rocket className="h-4 w-4 mr-2" />
                版本分配
              </TabsTrigger>
            </TabsList>

            <TabsContent value="namespaces" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>命名空间管理</CardTitle>
                  <CardDescription>
                    管理翻译命名空间，组织和隔离不同的翻译模块
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <NamespaceManagement />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="translations" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>翻译管理</CardTitle>
                  <CardDescription>
                    管理多语言翻译键值，支持19种语言
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <TranslationManagement />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="releases" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>版本发布</CardTitle>
                  <CardDescription>
                    管理环境间的版本发布、回滚和克隆
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <VersionManagementSimplified />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="assignment" className="mt-6">
              <VersionAssignment />
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  )
} 