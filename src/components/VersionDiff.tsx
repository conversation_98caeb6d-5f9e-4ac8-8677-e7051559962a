"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "sonner"
import { GitCompare, Plus, Minus, Edit, Check, Eye } from "lucide-react"

interface TranslationDiff {
  key: string
  type: 'added' | 'removed' | 'modified' | 'unchanged'
  current?: any
  previous?: any
  changes?: {
    language: string
    oldValue: string | null
    newValue: string | null
  }[]
}

interface VersionDiffData {
  currentVersion: {
    id: string
    version: string
    status: string
  }
  previousVersion: {
    id: string
    version: string
    status: string
  } | null
  summary: {
    added: number
    removed: number
    modified: number
    unchanged: number
    total: number
  }
  changes: TranslationDiff[]
}

interface VersionDiffProps {
  releaseId: string
  trigger?: React.ReactNode
  mode?: 'view' | 'publish'
  onPublishConfirm?: () => void
}

const languageNames: Record<string, string> = {
  zh_CN: "简体中文",
  zh_TW: "繁体中文", 
  en_US: "英文",
  ja_JP: "日文",
  ko_KR: "韩文",
  fr_FR: "法文",
  de_DE: "德文",
  es_ES: "西班牙文",
  pt_PT: "葡萄牙文",
  ru_RU: "俄文",
  ar_SA: "阿拉伯文",
  bn_BD: "孟加拉文",
  hi_IN: "印地文",
  th_TH: "泰文",
  vi_VN: "越南文",
  id_ID: "印尼文",
  tr_TR: "土耳其文",
  ur_PK: "乌尔都文",
  fa_IR: "波斯文"
}

export default function VersionDiff({ releaseId, trigger, mode = 'view', onPublishConfirm }: VersionDiffProps) {
  const [diffData, setDiffData] = useState<VersionDiffData | null>(null)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("summary")

  const fetchDiff = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/releases/${releaseId}/diff`)
      const data = await response.json()
      
      if (response.ok) {
        setDiffData(data.data)
      } else {
        toast.error(data.error || "获取版本对比失败")
      }
    } catch (error) {
      toast.error("获取版本对比失败")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && releaseId) {
      fetchDiff()
    }
  }, [open, releaseId])

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'added': return <Plus className="h-4 w-4 text-green-600" />
      case 'removed': return <Minus className="h-4 w-4 text-red-600" />
      case 'modified': return <Edit className="h-4 w-4 text-orange-600" />
      case 'unchanged': return <Check className="h-4 w-4 text-gray-400" />
      default: return null
    }
  }

  const getTypeBadge = (type: string) => {
    const variants = {
      added: "bg-green-100 text-green-800",
      removed: "bg-red-100 text-red-800", 
      modified: "bg-orange-100 text-orange-800",
      unchanged: "bg-gray-100 text-gray-600"
    }
    
    const labels = {
      added: "新增",
      removed: "删除",
      modified: "修改", 
      unchanged: "未变化"
    }

    return (
      <Badge className={variants[type as keyof typeof variants]}>
        {getTypeIcon(type)}
        <span className="ml-1">{labels[type as keyof typeof labels]}</span>
      </Badge>
    )
  }

  const renderLanguageChanges = (changes: TranslationDiff['changes']) => {
    if (!changes || changes.length === 0) return null

    return (
      <div className="space-y-2 mt-2">
        {changes.map((change, index) => (
          <div key={index} className="border rounded p-2 text-sm">
            <div className="font-medium text-gray-700 mb-1">
              {languageNames[change.language]} ({change.language})
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="text-xs text-gray-500 mb-1">原内容:</div>
                <div className="bg-red-50 p-1 rounded text-red-800 min-h-[24px]">
                  {change.oldValue || <span className="text-gray-400">空</span>}
                </div>
              </div>
              <div>
                <div className="text-xs text-gray-500 mb-1">新内容:</div>
                <div className="bg-green-50 p-1 rounded text-green-800 min-h-[24px]">
                  {change.newValue || <span className="text-gray-400">空</span>}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const filterChangesByType = (type: string) => {
    return diffData?.changes.filter(change => change.type === type) || []
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <GitCompare className="h-4 w-4 mr-2" />
            查看变更
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            版本变更对比
          </DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : !diffData ? (
          <div className="text-center py-8 text-gray-500">无法获取版本对比数据</div>
        ) : (
          <div className="space-y-6">
            {/* 版本信息 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">当前版本: {diffData.currentVersion.version}</div>
                <div className="text-sm text-gray-600">状态: {diffData.currentVersion.status}</div>
              </div>
              <div className="text-gray-400">vs</div>
              <div>
                {diffData.previousVersion ? (
                  <>
                    <div className="font-medium">对比版本: {diffData.previousVersion.version}</div>
                    <div className="text-sm text-gray-600">状态: {diffData.previousVersion.status}</div>
                  </>
                ) : (
                  <div className="text-gray-500">无对比版本</div>
                )}
              </div>
            </div>

            {/* 变更摘要 */}
            <Card>
              <CardHeader>
                <CardTitle>变更摘要</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{diffData.summary.added}</div>
                    <div className="text-sm text-gray-600">新增</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{diffData.summary.modified}</div>
                    <div className="text-sm text-gray-600">修改</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{diffData.summary.removed}</div>
                    <div className="text-sm text-gray-600">删除</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{diffData.summary.unchanged}</div>
                    <div className="text-sm text-gray-600">未变化</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 详细变更 */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="summary">全部 ({diffData.summary.total})</TabsTrigger>
                <TabsTrigger value="added">新增 ({diffData.summary.added})</TabsTrigger>
                <TabsTrigger value="modified">修改 ({diffData.summary.modified})</TabsTrigger>
                <TabsTrigger value="removed">删除 ({diffData.summary.removed})</TabsTrigger>
                <TabsTrigger value="unchanged">未变化 ({diffData.summary.unchanged})</TabsTrigger>
              </TabsList>

              <TabsContent value="summary">
                <ChangesList changes={diffData.changes} />
              </TabsContent>
              <TabsContent value="added">
                <ChangesList changes={filterChangesByType('added')} />
              </TabsContent>
              <TabsContent value="modified">
                <ChangesList changes={filterChangesByType('modified')} />
              </TabsContent>
              <TabsContent value="removed">
                <ChangesList changes={filterChangesByType('removed')} />
              </TabsContent>
              <TabsContent value="unchanged">
                <ChangesList changes={filterChangesByType('unchanged')} />
              </TabsContent>
            </Tabs>
            
            {/* 发布确认按钮 */}
            {mode === 'publish' && diffData && (
              <div className="flex justify-end gap-3 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => setOpen(false)}
                >
                  取消
                </Button>
                <Button
                  onClick={() => {
                    onPublishConfirm?.()
                    setOpen(false)
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  确认发布
                </Button>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )

  function ChangesList({ changes }: { changes: TranslationDiff[] }) {
    if (changes.length === 0) {
      return <div className="text-center py-8 text-gray-500">没有变更</div>
    }

    return (
      <div className="space-y-4">
        {changes.map((change, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <code className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                    {change.key}
                  </code>
                  {getTypeBadge(change.type)}
                </div>
              </div>

              {change.type === 'modified' && change.changes && (
                renderLanguageChanges(change.changes)
              )}

              {change.type === 'added' && change.current && (
                <div className="mt-2 p-2 bg-green-50 rounded">
                  <div className="text-sm text-green-800">
                    新增翻译键，包含 {Object.keys(change.current).filter(k => 
                      k.includes('_') && change.current[k]
                    ).length} 种语言的翻译
                  </div>
                </div>
              )}

              {change.type === 'removed' && change.previous && (
                <div className="mt-2 p-2 bg-red-50 rounded">
                  <div className="text-sm text-red-800">
                    删除翻译键，原包含 {Object.keys(change.previous).filter(k => 
                      k.includes('_') && change.previous[k]
                    ).length} 种语言的翻译
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }
}
